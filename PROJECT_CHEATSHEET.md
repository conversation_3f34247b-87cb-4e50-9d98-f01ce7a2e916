# 🚗 车店助手 - UniApp Vue3 TypeScript 项目快速上手指南

## 📋 项目概览
- **项目名称**: 车店助手 (汽修门店管理系统)
- **技术栈**: UniApp + Vue 3 + TypeScript + Vite
- **目标平台**: iOS + Android + 小程序 + H5
- **UI组件库**: uni-ui + uv-ui + 自定义组件

---

## 🧭 页面导航方法

### 1. 基础导航 API
```typescript
// 保留当前页面，跳转到应用内的某个页面
uni.navigateTo({
  url: '/pages/home/<USER>'
})

// 关闭当前页面，跳转到应用内的某个页面
uni.redirectTo({
  url: '/pages/login/login'
})

// 跳转到 tabBar 页面，并关闭其他所有非 tabBar 页面
uni.switchTab({
  url: '/pages/home/<USER>'
})

// 关闭所有页面，打开到应用内的某个页面
uni.reLaunch({
  url: '/pages/login/login'
})

// 关闭当前页面，返回上一页面或多级页面
uni.navigateBack({
  delta: 1 // 返回的页面数，如果 delta 大于现有页面数，则返回到首页
})
```

### 2. 项目中常用导航模式
```typescript
// 通用导航函数 (在 finance.vue 中使用)
const onNavigateTo = (url: string) => {
  uni.navigateTo({ url })
}

// 菜单点击导航 (在 home.vue 中使用)
const handleClickMenu = (path: string) => {
  uni.navigateTo({
    url: path,
    fail: (err) => {
      uni.showToast({
        title: "模块开发中",
        icon: "error",
      });
    },
  });
};

// 登录成功后跳转到主页
uni.switchTab({
  url: "/pages/home/<USER>",
})
```

### 3. 带参数导航示例
```typescript
// 跳转并传递参数
uni.navigateTo({ 
  url: "/pages/client/add?id=1" 
});

// 跳转到详情页
uni.navigateTo({ 
  url: "/pages/client/car-detail?id=" + car.id 
});

// 在目标页面接收参数
onLoad((options) => {
  const id = options.id;
  // 处理参数
});
```

---

## ⚙️ 页面配置 (pages.json)

### 1. 全局样式配置
```json
{
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "车店助手",
    "navigationBarBackgroundColor": "#086BDB",
    "backgroundColor": "#086BDB",
    "navigationStyle": "custom"
  }
}
```

### 2. TabBar 配置
```json
{
  "tabBar": {
    "color": "#333333",
    "selectedColor": "#086BDB",
    "borderStyle": "black",
    "backgroundColor": "#ffffff",
    "height": "20.2vw",
    "iconWidth": "6.53vw",
    "spacing": "10px",
    "iconfontSrc": "static/icons/iconfont.ttf",
    "list": [
      {
        "pagePath": "pages/home/<USER>",
        "text": "工作台",
        "iconfont": {
          "text": "\ue633",
          "selectedText": "\ue633",
          "color": "#333333",
          "selectedColor": "#086BDB",
          "size": "3.47vw"
        }
      }
      // ... 其他 tab 项
    ]
  }
}
```

### 3. Easycom 组件自动导入配置
```json
{
  "easycom": {
    "autoscan": true,
    "custom": {
      "^uv-(.*)": "@climblee/uv-ui/components/uv-$1/uv-$1.vue"
    }
  }
}
```

### 4. 页面路由配置
```json
{
  "pages": [
    {
      "path": "pages/login/login"
    },
    {
      "path": "pages/home/<USER>"
    },
    {
      "path": "pages/warehouse/warehouse"
    }
    // ... 更多页面
  ]
}
```

---

## 🌍 环境配置

### 1. 环境变量文件

#### 开发环境 (.env.development)
```bash
# H5开发环境
VITE_BASE_URL_H5="/api"
# APP开发环境  
VITE_BASE_URL_APP="http://taishifu2025.taishifu.cn"
VITE_AMAP_KEY="dd7b72af5c445ccb6c8320096d24630e"
```

#### 生产环境 (.env.production)
```bash
# H5生产环境
VITE_BASE_URL_H5="http://taishifu2025.taishifu.cn/"
# APP生产环境
VITE_BASE_URL_APP="http://taishifu2025.taishifu.cn/"
VITE_AMAP_KEY="b6c4eb36ad6d3e863eb7cb9ea8643aba"
```

### 2. API 基础地址配置
```typescript
// 在每个 API 文件中的环境判断
let VITE_BASE_URL = "";
// #ifdef APP-PLUS
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_APP;
// #endif
// #ifdef H5
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_H5;
// #endif
```

### 3. Vite 代理配置 (vite.config.ts)
```typescript
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://taishifu2025.taishifu.cn/',
        changeOrigin: true, 
        rewrite: (path) => path.replace(/^\/api/, ""),
      },
      "/location": {
        target: "https://restapi.amap.com",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/location/, ""),
      },
    },
  }
});
```

### 4. 构建命令
```bash
# 开发环境
npm run dev:h5          # H5开发
npm run dev:mp-weixin   # 微信小程序开发

# 生产构建
npm run build:h5        # H5构建
npm run build:mp-weixin # 微信小程序构建
```

---

## 🧩 核心组件配置

### 1. 自定义组件

#### lButton 按钮组件
```vue
<template>
  <lButton>登录</lButton>
</template>
```
**特点**: 圆角按钮，支持插槽，蓝色主题

#### lInput 输入框组件
```vue
<template>
  <lInput 
    type="phone" 
    shape="white" 
    v-model="phone" 
    placeholder="请输入手机号"
  />
  <lInput 
    type="code" 
    v-model="code" 
    @code-click="sendCode"
  />
  <lInput 
    type="pass" 
    v-model="password"
  />
</template>
```
**Props**:
- `type`: 'phone' | 'code' | 'pass'
- `shape`: 'white' | 'black'  
- `modelValue`: 双向绑定值
- `placeholder`: 占位符
- `disabled`: 是否禁用

**Events**:
- `code-click`: 验证码点击事件
- `update:modelValue`: 值更新事件

#### lCalendar 日历组件
```vue
<template>
  <lCalendar v-model="selectedDate" />
</template>
```

#### lTable 表格组件
```vue
<template>
  <lTable :data="tableData" :columns="columns" />
</template>
```

### 2. UI 组件库

#### uni-ui 组件 (已配置 easycom)
```vue
<template>
  <!-- 直接使用，无需导入 -->
  <uni-badge text="1"></uni-badge>
  <uni-card title="卡片标题">内容</uni-card>
  <uni-list>
    <uni-list-item title="列表项" />
  </uni-list>
</template>
```

#### uv-ui 组件 (已配置 easycom)
```vue
<template>
  <!-- 直接使用，无需导入 -->
  <uv-button type="primary">按钮</uv-button>
  <uv-input v-model="value" placeholder="请输入" />
</template>
```

### 3. 工具函数

#### 文件处理工具 (utils/file.ts)
```typescript
import { importFile } from "@/utils/file";

// 导入静态资源
const iconUrl = importFile("../static/images/icon.png");

// 上传文件
import { uploadFile } from "@/utils/file";
const result = await uploadFile(1); // 上传1张图片
```

#### 请求工具 (utils/request.ts)
```typescript
import { uniRequest } from "@/utils/request";

// 发送请求
const response = await uniRequest({
  url: 'https://api.example.com/data',
  method: 'POST',
  data: { key: 'value' }
});
```

### 4. 样式配置

#### 全局样式变量 (styles/variable.scss)
```scss
// 在 vite.config.ts 中已配置自动导入
// 可直接在组件中使用变量
.my-component {
  color: $primary-color; // 使用全局变量
}
```

#### 组件样式规范
```vue
<style scoped lang="scss">
// 使用 scoped 避免样式污染
// 使用 scss 预处理器
// 支持 vw 单位适配不同屏幕
.component {
  width: 100vw;
  height: 10vw;
  font-size: 3.47vw;
}
</style>
```

---

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install
# 或
pnpm install
```

### 2. 启动开发服务器
```bash
# H5 开发
npm run dev:h5

# 微信小程序开发
npm run dev:mp-weixin
```

### 3. 项目结构
```
src/
├── api/           # API 接口
├── components/    # 自定义组件
├── pages/         # 页面文件
├── static/        # 静态资源
├── styles/        # 全局样式
├── utils/         # 工具函数
├── App.vue        # 应用入口
├── main.ts        # 主入口文件
└── pages.json     # 页面配置
```

### 4. 开发规范
- 使用 TypeScript 进行类型检查
- 组件使用 `<script setup>` 语法
- 样式使用 SCSS 预处理器
- 遵循 Vue 3 Composition API 规范
- 使用 ESLint 进行代码规范检查

---

## 📱 平台特性

### 条件编译
```typescript
// #ifdef H5
// H5 平台特有代码
// #endif

// #ifdef APP-PLUS  
// App 平台特有代码
// #endif

// #ifdef MP-WEIXIN
// 微信小程序特有代码
// #endif
```

### 平台 API 使用
```typescript
// 统一使用 uni API
uni.showToast({ title: '提示信息' });
uni.navigateTo({ url: '/pages/home/<USER>' });
uni.request({ url: 'https://api.example.com' });
```

---

*最后更新: 2024年12月*
