import { defineConfig } from "vite";
import uni from "@dcloudio/vite-plugin-uni";
import vueSetupExtend from "vite-plugin-vue-setup-extend";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [uni(), vueSetupExtend()],
  resolve: {
    alias: {
      "@": "/src",
    },
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://taishifu2025.taishifu.cn/',
        changeOrigin: true, 
        rewrite: (path) => path.replace(/^\/api/, ""),
      },
      "/location": {
        target: "https://restapi.amap.com",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/location/, ""),
      },
    },
  },

  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@use "@/styles/variable.scss" as *;`,
      },
    },
  },
});
