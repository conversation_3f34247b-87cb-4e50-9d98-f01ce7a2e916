import CryptoJS from "crypto-js";
export default {
  // 模拟登录接口
  login: (options: any) => {
    const { password } = JSON.parse(options.body);
    // md5解密
    const md5Password = CryptoJS.MD5(password).toString();
    if (md5Password === "123456") {
      return {
        code: 200,
        message: "登录成功",
        data: {
          token: "1234567890",
        },
      };
    } else {
      return {
        code: 401,
        message: "账号或密码错误",
        data: null,
      };
    }
  },
  // 模拟注册接口
  register: (options: any) => {
    return {
      code: 200,
      message: "注册成功",
      data: {
        token: "1234567890",
      },
    };
  },

  // 模拟绑定手机号接口
  bindPhone: (options: any) => {
    return {
      code: 200,
      message: "绑定手机号成功",
    };
  },

  // 模拟找回密码接口
  findPassword: (options: any) => {
    return {
      code: 200,
      message: "找回密码成功",
    };
  },
  //   模拟获取app信息接口
  getAppInfo: (options: any) => {
    return {
      code: 200,
      message: "获取app信息成功",
      data: {
        appName: "车店助手",
        appDesc: "助力汽修人开源节流创收转型",
      },
    };
  },
};
