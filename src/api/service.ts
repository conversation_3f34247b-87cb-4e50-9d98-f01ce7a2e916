// uni.request 请求封装
import { uniRequest } from "@/utils/request";
import type { TableData, ApiResponse } from "@/utils/type";
let VITE_BASE_URL = "";
// #ifdef APP-PLUS
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_APP;
// #endif
// #ifdef H5
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_H5;
// #endif

/**
 * 服务管理相关接口
 */
enum ServiceType {
  GET_SERVICE_DETAIL = "/storeapi/carService/getOne",
  ADD_SERVICE = "/storeapi/carService/addOne",
  EDIT_SERVICE = "/storeapi/carService/editOne",
  DELETE_SERVICE = "/storeapi/carService/delOne",
  GET_LOCAL_SERVICE_LIST = "/storeapi/carService/getList",
  GET_ALL_SERVICE_LIST = "/storeapi/carService/getAllList",
  DOWNLOAD_CLOUD_SERVICE = "/storeapi/carService/download",
}

// 车辆服务详情
export function getServiceDetail(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + ServiceType.GET_SERVICE_DETAIL,
    method: "POST",
    data,
  });
}

// 新增车辆服务
export function addService(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + ServiceType.ADD_SERVICE,
    method: "POST",
    data,
  });
}

// 修改车辆服务
export function editService(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + ServiceType.EDIT_SERVICE,
    method: "POST",
    data,
  });
}

// 删除车辆服务
export function deleteService(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + ServiceType.DELETE_SERVICE,
    method: "POST",
    data,
  });
}

// 获取车辆服务列表(本地)
export function getLocalServiceList(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + ServiceType.GET_LOCAL_SERVICE_LIST,
    method: "POST",
    data,
  });
}

// 获取车辆服务列表(本地+云端)
export function getAllServiceList(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + ServiceType.GET_ALL_SERVICE_LIST,
    method: "POST",
    data,
  });
}

// 下载云端服务
export function downloadCloudService(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + ServiceType.DOWNLOAD_CLOUD_SERVICE,
    method: "POST",
    data,
  });
}
