// uni.request 请求封装
import { uniRequest } from "@/utils/request";
import type { TableData } from "@/utils/type";
let VITE_BASE_URL = "";
// #ifdef APP-PLUS
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_APP;
// #endif
// #ifdef H5
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_H5;
// #endif

/**
 * 采购管理相关接口
 */
enum PurchaseType {
  GET_PURCHASE_DETAIL = "/storeapi/stockIncome/getOne",
  GET_PURCHASE_LIST = "/storeapi/stockIncome/getList",
  SUBMIT_PURCHASE = "/storeapi/stockIncome/saveOrder",
  CANCEL_PURCHASE = "/storeapi/stockIncome/cancelOrder",
  CONFIRM_PURCHASE = "/storeapi/stockIncome/income",
  REVOKE_PURCHASE = "/storeapi/stockIncome/cancelIncome",
  CONFIRM_PAYMENT = "/storeapi/stockIncome/payOrder",
  REVOKE_PAYMENT = "/storeapi/stockIncome/cancelPayOrder",
  DELETE_PURCHASE = "/storeapi/stockIncome/delStockOrder",
}

// 采购入库单详情
export function getPurchaseDetail(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PurchaseType.GET_PURCHASE_DETAIL,
    method: "POST",
    data,
  });
}

// 采购入库单列表
export function getPurchaseList(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PurchaseType.GET_PURCHASE_LIST,
    method: "POST",
    data,
  });
}

// 提交入库
export function submitPurchase(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PurchaseType.SUBMIT_PURCHASE,
    method: "POST",
    data,
  });
}

// 采购入库单作废
export function cancelPurchase(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PurchaseType.CANCEL_PURCHASE,
    method: "POST",
    data,
  });
}

// 确认入库
export function confirmPurchase(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PurchaseType.CONFIRM_PURCHASE,
    method: "POST",
    data,
  });
}

// 撤销入库
export function revokePurchase(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PurchaseType.REVOKE_PURCHASE,
    method: "POST",
    data,
  });
}

// 确认付款
export function confirmPayment(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PurchaseType.CONFIRM_PAYMENT,
    method: "POST",
    data,
  });
}

// 撤销付款
export function revokePayment(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PurchaseType.REVOKE_PAYMENT,
    method: "POST",
    data,
  });
}

// 删除采购入库单
export function deletePurchase(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PurchaseType.DELETE_PURCHASE,
    method: "POST",
    data,
  });
}
