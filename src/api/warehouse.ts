// uni.request 请求封装
import { uniRequest } from "@/utils/request";
import type { TableData } from "@/utils/type";
let VITE_BASE_URL = "";
// #ifdef APP-PLUS
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_APP;
// #endif
// #ifdef H5
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_H5;
// #endif

/**
 * 供应商管理相关接口
 */
enum SupplierType {
  ADD_SUPPLIER = "/storeapi/storeSupplier/addOne",
  EDIT_SUPPLIER = "/storeapi/storeSupplier/editOne",
  DELETE_SUPPLIER = "/storeapi/storeSupplier/delOne",
  GET_SUPPLIER_LIST = "/storeapi/storeSupplier/getList",
  GET_SUPPLIER_DETAIL = "/storeapi/storeSupplier/getOne",
}

// 添加供应商
export function addSupplier(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + SupplierType.ADD_SUPPLIER,
    method: "POST",
    data,
  });
}

// 编辑供应商
export function editSupplier(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + SupplierType.EDIT_SUPPLIER,
    method: "POST",
    data,
  });
}

// 供应商列表
export function getSupplierList(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + SupplierType.GET_SUPPLIER_LIST,
    method: "POST",
    data,
  });
}

// 供应商详情
export function getSupplierDetail(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + SupplierType.GET_SUPPLIER_DETAIL,
    method: "POST",
    data,
  });
}

// 删除供应商
export function deleteSupplier(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + SupplierType.DELETE_SUPPLIER,
    method: "POST",
    data,
  });
}

/**
 * 仓库管理相关接口
 */
enum WarehouseType {
  ADD_WAREHOUSE = "/storeapi/depot/addOne",
  EDIT_WAREHOUSE = "/storeapi/depot/editOne",
  DELETE_WAREHOUSE = "/storeapi/depot/delOne",
  GET_WAREHOUSE_LIST = "/storeapi/depot/getList",
  GET_WAREHOUSE_DETAIL = "/storeapi/depot/getOne",
}   
// 新增仓库
export function addWarehouse(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + WarehouseType.ADD_WAREHOUSE,
    method: "POST",
    data,
  });
}

// 编辑仓库
export function editWarehouse(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + WarehouseType.EDIT_WAREHOUSE,
    method: "POST",
    data,
  });
}

// 删除仓库
export function deleteWarehouse(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + WarehouseType.DELETE_WAREHOUSE,
    method: "POST",
    data,
  });
}

// 仓库列表
export function getWarehouseList(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + WarehouseType.GET_WAREHOUSE_LIST,
    method: "POST",
    data,
  });
}
