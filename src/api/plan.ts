import { uniRequest } from "@/utils/request";
import type { TableData } from "@/utils/type";
let VITE_BASE_URL = "";
// #ifdef APP-PLUS
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_APP;
// #endif
// #ifdef H5
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_H5;
// #endif

/**
 * 预约管理相关接口
 */
enum PlanType {
  GET_PLAN_LIST = "/storeapi/reserve/getList",
  GET_CALENDAR_PLAN = "/storeapi/reserve/getCalendar",
  ADD_PLAN = "/storeapi/reserve/addOne",
  GET_PLAN_DETAIL = "/storeapi/reserve/getOne",
  SET_PLAN_STATUS = "/storeapi/reserve/setState",
  SET_PLAN_REMINDED = "/storeapi/reserve/readReserve",
  GET_CONFIRMED_PLAN_LIST = "/storeapi/reserve/getStoreList",
  GET_PLAN_CATEGORY_LIST = "/storeapi/reserveCategory/getList",
}

// 预约列表
export function getPlanList(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PlanType.GET_PLAN_LIST,
    method: "POST",
    data,
  });
}

// 获取日历预约
export function getCalendarPlan(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PlanType.GET_CALENDAR_PLAN,
    method: "POST",
    data,
  });
}

// 添加预约
export function addPlan(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PlanType.ADD_PLAN,
    method: "POST",
    data,
  });
}

// 预约详情
export function getPlanDetail(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PlanType.GET_PLAN_DETAIL,
    method: "GET",
    data: params,
  });
}

// 状态设置
export function setPlanStatus(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PlanType.SET_PLAN_STATUS,
    method: "GET",
    data: params,
  });
}

// 设置预约已提醒
export function setPlanReminded(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PlanType.SET_PLAN_REMINDED,
    method: "GET",
    data: params,
  });
}

// 获取店铺确认预约列表
export function getConfirmedPlanList(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PlanType.GET_CONFIRMED_PLAN_LIST,
    method: "POST",
    data,
  });
}

// 预约分类列表
export function getPlanCategoryList(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PlanType.GET_PLAN_CATEGORY_LIST,
    method: "POST",
    data,
  });
}
