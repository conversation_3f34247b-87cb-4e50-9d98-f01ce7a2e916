// uni.request 请求封装
import { uniRequest } from "@/utils/request";
import type { TableData } from "@/utils/type";
let VITE_BASE_URL = "";
// #ifdef APP-PLUS
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_APP;
// #endif
// #ifdef H5
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_H5;
// #endif
/**
 * 获取部门与员工关系相关接口
 */
enum TeamType {
  GET_DEPARTMENT_STAFF_RELATION = "/storeapi/team/getStaffList",
}

// 获取部门与员工的关联关系
export function getDepartmentStaffRelation(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + TeamType.GET_DEPARTMENT_STAFF_RELATION,
    method: "GET",
    data: params,
  });
}

/**
 * 员工管理相关接口
 */
enum StaffType {
  GET_STAFF_DETAIL = "/storeapi/team/getOneStaff",
  CREATE_STAFF = "/storeapi/team/addOneStaff",
  GET_DEPARTMENT_LIST = "/storeapi/team/getDepartmentList",
  EDIT_STAFF = "/storeapi/team/editOneStaff",
  DELETE_STAFF = "/storeapi/team/deleteOneStaff",
}
// 获取员工详情
export function getStaffDetail(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + StaffType.GET_STAFF_DETAIL,
    method: "GET",
    data: params,
  });
}

// 新增员工
export function createStaff(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + StaffType.CREATE_STAFF,
    method: "POST",
    data,
  });
}

// 获取部门列表
export function getDepartmentList(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + StaffType.GET_DEPARTMENT_LIST,
    method: "GET",
    data: params,
  });
}

// 编辑员工
export function editStaff(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + StaffType.EDIT_STAFF,
    method: "POST",
    data,
  });
}

// 删除员工
export function deleteStaff(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + StaffType.DELETE_STAFF,
    method: "POST",
    data,
  });
}

/**
 * 部门权限管理相关接口
 */
enum DepartmentType {
  GET_DEPARTMENT_PERMISSION = "/storeapi/team/getDepartmentJurisdictionInfo",
  SAVE_DEPARTMENT_PERMISSION = "/storeapi/store/team/saveDepartmentJurisdictionInfo",
  GET_STAFF_PERMISSION = "/storeapi/team/getStaffJurisdictionInfo",
  SAVE_STAFF_PERMISSION = "/storeapi/team/saveStaffJurisdictionInfo",
}
// 获取部门权限信息
export function getDepartmentPermission(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + DepartmentType.GET_DEPARTMENT_PERMISSION,
    method: "GET",
    data: params,
  });
}

// 获取保存部门权限信息
export function saveDepartmentJurisdictionInfo(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + DepartmentType.SAVE_DEPARTMENT_PERMISSION,
    method: "POST",
    data: params,
  });
}

// 获取员工个人权限
export function getStaffPermission(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + DepartmentType.GET_STAFF_PERMISSION,
    method: "GET",
    data: params,
  });
}

// 保存员工个人权限
export function saveStaffJurisdictionInfo(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + DepartmentType.SAVE_STAFF_PERMISSION,
    method: "POST",
    data: params,
  });
}
