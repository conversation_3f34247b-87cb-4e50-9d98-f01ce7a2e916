import { uniRequest } from "@/utils/request";
import type { TableData } from "@/utils/type";
let VITE_BASE_URL = "";
// #ifdef APP-PLUS
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_APP;
// #endif
// #ifdef H5
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_H5;
// #endif

/**
 * 门店端接口
 */
enum StoreType {
  GET_STORE_INFO = "/storeapi/store/getInfo",
  CERTIFICATION = "/storeapi/store/updateInfo",
  GET_SHAREHOLDER_LIST = "/storeapi/store/getShareholderList",
  TRANSFER = "/storeapi/store/assignment",
}

// 获取门店信息
export function getStoreInfo(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + StoreType.GET_STORE_INFO,
    method: "GET",
    data: params,
  });
}

// 门店认证
export function certification(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + StoreType.CERTIFICATION,
    method: "POST",
    data: params,
  });
}

// 获取股东列表
export function getShareholderList(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + StoreType.GET_SHAREHOLDER_LIST,
    method: "GET",
    data: params,
  });
}

// 转让
export function transfer(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + StoreType.TRANSFER,
    method: "POST",
    data: params,
  });
}
