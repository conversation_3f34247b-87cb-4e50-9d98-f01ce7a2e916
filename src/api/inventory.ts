// uni.request 请求封装
import { uniRequest } from "@/utils/request";
import type { TableData } from "@/utils/type";
let VITE_BASE_URL = "";
// #ifdef APP-PLUS
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_APP;
// #endif
// #ifdef H5
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_H5;
// #endif
enum InventoryType {
  GET_INVENTORY_DETAIL = "/storeapi/stockTake/getOne",
  GET_INVENTORY_LIST = "/storeapi/stockTake/getList",
  ADD_INVENTORY = "/storeapi/stockTake/addOne",
  EDIT_INVENTORY = "/storeapi/stockTake/editOne",
  REVoke_INVENTORY = "/storeapi/stockTake/cancelTake",
  CANCEL_INVENTORY = "/storeapi/stockTake/cancelOrder",
}
/**
 * 库存盘点相关接口
 */

// 库存盘点详情
export function getInventoryDetail(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + InventoryType.GET_INVENTORY_DETAIL,
    method: "POST",
    data,
  });
}

// 库存盘点单列表
export function getInventoryList(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + InventoryType.GET_INVENTORY_LIST,
    method: "POST",
    data,
  });
}

// 新增库存盘点单
export function addInventory(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + InventoryType.ADD_INVENTORY,
    method: "POST",
    data,
  });
}

// 编辑库存盘点单
export function editInventory(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + InventoryType.EDIT_INVENTORY,
    method: "POST",
    data,
  });
}

// 撤销库存盘点
export function revokeInventory(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + InventoryType.REVoke_INVENTORY,
    method: "POST",
    data,
  });
}

// 作废库存盘点
export function cancelInventory(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + InventoryType.CANCEL_INVENTORY,
    method: "POST",
    data,
  });
}
