// uni.request 请求封装
import { uniRequest } from "@/utils/request";
import type { TableData } from "@/utils/type";
let VITE_BASE_URL = "";
// #ifdef APP-PLUS
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_APP;
// #endif
// #ifdef H5
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_H5;
// #endif

enum AccessoryType {
  GET_LOCAL_ACCESSORY_LIST = "/storeapi/product/getList",
  GET_VEHICLE_ACCESSORY_LIST = "/storeapi/product/getAllList",
  DOWNLOAD_CLOUD_ACCESSORY = "/storeapi/product/download",
  GET_VEHICLE_ACCESSORY_DETAIL = "/storeapi/product/getOne",
  ADD_ACCESSORY_PRODUCT = "/storeapi/product/addOne",
  EDIT_ACCESSORY_PRODUCT = "/storeapi/product/editOne",
  DELETE_VEHICLE_ACCESSORY = "/storeapi/product/delOne",
  GET_ACCESSORY_TYPE_LIST = "/storeapi/product/getType",
}
/**
 * 配件管理相关接口
 */

// 配件列表（本地）
export function getLocalAccessoryList(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + AccessoryType.GET_LOCAL_ACCESSORY_LIST,
    method: "POST",
    data,
  });
}

// 车辆配件列表（本地+云端）
export function getVehicleAccessoryList(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + AccessoryType.GET_VEHICLE_ACCESSORY_LIST,
    method: "POST",
    data,
  });
}

// 下载云端配件产品
export function downloadCloudAccessory(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + AccessoryType.DOWNLOAD_CLOUD_ACCESSORY,
    method: "POST",
    data,
  });
}

// 车辆配件产品详情
export function getVehicleAccessoryDetail(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + AccessoryType.GET_VEHICLE_ACCESSORY_DETAIL,
    method: "POST",
    data,
  });
}

// 新增配件产品
export function addAccessoryProduct(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + AccessoryType.ADD_ACCESSORY_PRODUCT,
    method: "POST",
    data,
  });
}

// 编辑配件产品
export function editAccessoryProduct(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + AccessoryType.EDIT_ACCESSORY_PRODUCT,
    method: "POST",
    data,
  });
}

// 删除车辆配件产品
export function deleteVehicleAccessory(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + AccessoryType.DELETE_VEHICLE_ACCESSORY,
    method: "POST",
    data,
  });
}

// 配件产品类型列表
export function getAccessoryTypeList(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + AccessoryType.GET_ACCESSORY_TYPE_LIST,
    method: "POST",
    data,
  });
}


