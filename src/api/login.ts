// uni.request 请求封装
import { uniRequest } from "@/utils/request";
import type { TableData } from "@/utils/type";
let VITE_BASE_URL = "";
// #ifdef APP-PLUS
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_APP;
// #endif
// #ifdef H5
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_H5;
// #endif
enum LoginType {
  login = "/storeapi/login",
  codeLogin = "/storeapi/codeLogin",
  loginout = "/storeapi/store/storeuser/loginout",
  info = "/storeapi/storeuser/info",
  register = "/storeapi/register",
  changePassword = "/storeapi/changePassword",
  getList = "/storeapi/storeuser/getList",
  configRemindAndPrice = "/storeapi/storeuser/configRemindAndPrice",
  getUserInfo = "/storeapi/storeuser/getUserInfo",
  logOff = "/storeapi/storeuser/logOff",
  sendCode = "/storeapi/sms/sendCode",
  uploadUserPhones = "/storeapi/uploadUserPhones",
  getLocationOfShow = "/storeapi/getLocationOfShow",
  getLocationInfo = "/storeapi/getLocationInfo",  
}

/**
 * 登录相关接口
 */
// 门店端登录(账号密码登录)
export function loginByAccount(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + LoginType.login,
    method: "POST",
    data,
  });
}

// 门店端登录(验证码登录)
export function loginByCode(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + LoginType.codeLogin,
    method: "POST",
    data,
  });
}

// 退出登录
export function logout(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + LoginType.loginout,
    method: "GET",
    data: params,
  });
}

// 获取用户信息
export function getUserInfo(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + LoginType.info,
    method: "GET",
    data: params,
  });
}

// 注册
export function register(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + LoginType.register,
    method: "POST",
    data,
  });
}

// 重置密码
export function resetPassword(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + LoginType.changePassword,
    method: "POST",
    data,
  });
}

// 门店员工列表
export function getStoreStaffList(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + LoginType.getList,
    method: "GET",
    data: params,
  });
}

// 配置提醒与加价
export function setRemindAndMarkup(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + LoginType.configRemindAndPrice,
    method: "POST",
    data,
  });
}

// 获取用户信息
export function getStoreUserInfo(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + LoginType.getUserInfo,
    method: "GET",
    data: params,
  });
}

// 注销
export function unregister(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + LoginType.logOff,
    method: "POST",
    data,
  });
}

// 获取验证码
export function getCodeApi(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + LoginType.sendCode,
    method: "POST",
    data,
  });
}

// 上传通讯录
export function uploadContacts(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + LoginType.uploadUserPhones,
    method: "POST",
    data,
  });
}

// 省市区数据
export function getAreaData(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + LoginType.getLocationOfShow,
    method: "POST",
    data: params,
  });
}

// 根据定位获取省市区
export function getLocationInfo(params: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + LoginType.getLocationInfo,
    method: "POST",
    data: params,
  });
}
