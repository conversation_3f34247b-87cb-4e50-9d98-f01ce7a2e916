// uni.request 请求封装
import { uniRequest } from "@/utils/request";
import type { TableData } from "@/utils/type";
let VITE_BASE_URL = "";
// #ifdef APP-PLUS
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_APP;
// #endif
// #ifdef H5
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_H5;
// #endif

/**
 * 套餐管理相关接口
 */
enum PackageType {
  GET_PACKAGE_LIST = "/storeapi/package/getStorePackageList",
  GET_PACKAGE_DETAIL = "/storeapi/package/getOneStorePackage",
  GET_VEHICLE_PACKAGE_LIST = "/storeapi/package/getCarPackageList",
  GET_VEHICLE_PACKAGE_DETAIL = "/storeapi/package/getOneCarPackage",
  GET_PACKAGE_USAGE_RECORD = "/storeapi/package/getCarUsePackageLogList",
  ADD_PACKAGE = "/storeapi/package/addStorePackage",
  CANCEL_PACKAGE = "/storeapi/package/tovoidStorePackage",
  ACTIVATE_PACKAGE = "/storeapi/package/openStorePackage",
  GET_AVAILABLE_PACKAGES_BY_PLATE = "/storeapi/package/getCarAllowPackageList",
  REFUND_PACKAGE = "/storeapi/package/returnPackage",
}
// 套餐列表
export function getPackageList(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PackageType.GET_PACKAGE_LIST,
    method: "POST",
    data,
  });
}

// 套餐详情
export function getPackageDetail(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PackageType.GET_PACKAGE_DETAIL,
    method: "POST",
    data,
  });
}

// 车辆套餐列表
export function getVehiclePackageList(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PackageType.GET_VEHICLE_PACKAGE_LIST,
    method: "POST",
    data,
  });
}

// 车辆套餐详情
export function getVehiclePackageDetail(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PackageType.GET_VEHICLE_PACKAGE_DETAIL,
    method: "POST",
    data,
  });
}

// 套餐使用记录
export function getPackageUsageRecord(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PackageType.GET_PACKAGE_USAGE_RECORD,
    method: "POST",
    data,
  });
}

// 新增套餐
export function addPackage(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PackageType.ADD_PACKAGE,
    method: "POST",
    data,
  });
}

// 作废套餐
export function cancelPackage(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PackageType.CANCEL_PACKAGE,
    method: "POST",
    data,
  });
}

// 为客户或车辆开通套餐
export function activatePackage(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PackageType.ACTIVATE_PACKAGE,
    method: "POST",
    data,
  });
}

// 通过车牌号获取可使用套餐列表
export function getAvailablePackagesByPlate(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PackageType.GET_AVAILABLE_PACKAGES_BY_PLATE,
    method: "POST",
    data,
  });
}

// 套餐退卡
export function refundPackage(data: TableData) {
  return uniRequest({
    url: VITE_BASE_URL + PackageType.REFUND_PACKAGE,
    method: "POST",
    data,
  });
} 