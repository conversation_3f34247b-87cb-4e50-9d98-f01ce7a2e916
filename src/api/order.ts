// uni.request 请求封装
import { uniRequest } from "@/utils/request";
import type { TableData } from "@/utils/type";
let VITE_BASE_URL = "";
// #ifdef APP-PLUS
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_APP;
// #endif
// #ifdef H5
VITE_BASE_URL = import.meta.env.VITE_BASE_URL_H5;
// #endif
/**
 * 订单相关接口枚举
 */
enum OrderApi {
  CREATE = "/storeapi/carOrder/saveOrder", // 创建/编辑订单
  LIST = "/storeapi/carOrder/getOrderList", // 订单列表
  DETAIL = "/storeapi/carOrder/getOneOrder", // 订单详情
  HISTORY = "/storeapi/carOrder/getOneCarHistory", // 订单维修记录
  SET_STORE_NOTE = "/storeapi/carOrder/setStoreNote", // 设置店主建议
  SET_CHECK_CAR_NOTE = "/storeapi/carOrder/setCheckCarNote", // 设置检查意见
  ORDER_REPAIR = "/storeapi/carOrder/orderRepair", // 返修
  CLICK_RETURN_CAR = "/storeapi/carOrder/clickReturnCar", // 提车
  OCR_VIN = "/storeapi/carOrder/ocrVin", // 识别车架号
  OCR_CAR_CARD = "/storeapi/carOrder/ocrCarCard", // 识别车牌号
  ORDER_DELETE = "/storeapi/carOrder/orderDelete", // 删除订单
  GET_ORDER_CHECKOUT = "/storeapi/carOrder/getOrderCheckout", // 获取结算单
  CLICK_ACCOUNTS = "/storeapi/carOrder/clickAccounts", // 订单结算
}

/**
 * 通用订单请求方法
 * @param api OrderApi 枚举值
 * @param data 请求参数
 * @param method 请求方法（默认POST，部分GET）
 */
function orderRequest(
  api: OrderApi,
  data: TableData = {},
  method: "GET" | "POST" = "POST"
) {
  return uniRequest({
    url: VITE_BASE_URL + api,
    method,
    [method === "GET" ? "data" : "data"]: data,
  });
}

// 具体业务方法（全部严格按OrderApi枚举值导出）
export const createOrder = (data: TableData) =>
  orderRequest(OrderApi.CREATE, data, "POST");
export const getOrderList = (params: TableData) =>
  orderRequest(OrderApi.LIST, params, "POST");
export const getOrderDetail = (params: TableData) =>
  orderRequest(OrderApi.DETAIL, params, "POST");
export const getOrderHistory = (params: TableData) =>
  orderRequest(OrderApi.HISTORY, params, "POST");
export const setStoreNote = (data: TableData) =>
  orderRequest(OrderApi.SET_STORE_NOTE, data, "POST");
export const setCheckCarNote = (data: TableData) =>
  orderRequest(OrderApi.SET_CHECK_CAR_NOTE, data, "POST");
export const orderRepair = (data: TableData) =>
  orderRequest(OrderApi.ORDER_REPAIR, data, "POST");
export const clickReturnCar = (data: TableData) =>
  orderRequest(OrderApi.CLICK_RETURN_CAR, data, "POST");
export const ocrVin = (data: TableData) =>
  orderRequest(OrderApi.OCR_VIN, data, "POST");
export const ocrCarCard = (data: TableData) =>
  orderRequest(OrderApi.OCR_CAR_CARD, data, "POST");
export const orderDelete = (data: TableData) =>
  orderRequest(OrderApi.ORDER_DELETE, data, "POST");
export const getOrderCheckout = (params: TableData) =>
  orderRequest(OrderApi.GET_ORDER_CHECKOUT, params, "GET");
export const clickAccounts = (data: TableData) =>
  orderRequest(OrderApi.CLICK_ACCOUNTS, data, "POST");
