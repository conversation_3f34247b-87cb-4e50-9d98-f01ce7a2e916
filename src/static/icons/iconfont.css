@font-face {
  font-family: "iconfont"; /* Project id 4859343 */
  src: url('iconfont.woff2?t=1742111350432') format('woff2'),
       url('iconfont.woff?t=1742111350432') format('woff'),
       url('iconfont.ttf?t=1742111350432') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.car-cloud:before {
  content: "\e6b7";
}

.car-gongzuotai:before {
  content: "\e633";
}

.car-tongxunlu:before {
  content: "\e62e";
}

.car-xiaoxi:before {
  content: "\e600";
}

.car-yonghu:before {
  content: "\e604";
}

