<template>
  <scroll-view
    scroll-y
    :refresher-enabled="true"
    :refresher-triggered="status === 'loading'"
    @refresherrefresh="onRefresh"
    @scrolltolower="onLoadMore"
    :style="{ height: height }"
  >
    <view class="table">
      <view class="table-row table-header">
        <view
          class="table-cell"
          v-for="(header, index) in headers"
          :key="index"
          :style="{ width: header.width }"
          >{{ header.title }}</view
        >
      </view>
      <view
        class="table-row"
        v-for="(row, rowIndex) in tableData"
        :key="rowIndex"
      >
        <view
          class="table-cell"
          v-for="(cell, cellIndex) in headers"
          :key="cellIndex"
          :style="{ width: cell.width }"
        >
          <!-- 自定义插槽  -->
          <template v-if="cell.slot">
            <slot :name="cell.slot" :row="row"></slot>
          </template>
          <!-- 默认插槽 -->
          <view class="table-cell-text" v-else>{{ row[cell.columnKey] }}</view>
        </view>
      </view>
    </view>
    <uni-load-more :status="status"></uni-load-more>
  </scroll-view>
</template>

<script setup lang="ts">
import type { TableColumn, TableData } from "@/utils/type";
import { ref } from "vue";
interface FetchDataParams {
  page?: number;
  pageSize?: number;
}

const {
  headers = [],
  height = "100%",
  fetchData,
} = defineProps<{
  headers: TableColumn[];
  height?: string;
  fetchData: (params?: FetchDataParams) => Promise<TableData[]>;
}>();

const status = ref("more");
const page = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref<TableData[]>([]);
const onRefresh = () => {
  status.value = "loading";
  page.value = 1;
  tableData.value = [];
  onLoadMore();
};

const onLoadMore = async () => {
  console.log("onLoadMore");
  // if (status.value === "loading") return;
  // if (status.value === "no-more") return;
  status.value = "loading";
  const res = await fetchData({ page: page.value, pageSize: pageSize.value });
  console.log(res);
  if (res.length === 0) {
    status.value = "no-more";
  } else {
    tableData.value = [...tableData.value, ...res];
    page.value++;
  }
  status.value = "more";
};
</script>

<style scoped lang="less">
.table {
  .table-row {
    display: flex;
    border-bottom: 1px solid #f1f1f1;
    line-height: 80upx;
    text-align: center;
    .table-cell {
      flex: 1;
      font-size: 24upx;
      .table-cell-text {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  .table-header {
    background-color: #f1f1f1;
    font-size: 30upx;
    text-align: center;
    font-weight: bold;
    line-height: 80upx;
    color: #333333;
  }
}
</style>
