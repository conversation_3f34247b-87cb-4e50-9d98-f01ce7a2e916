<template>
  <view class="calendar">
    <view class="calendar-header">
      <view class="calendar-header-btn" @click="prevMonth">
        <text>上一月</text>
      </view>
      <view class="calendar-header-title">{{ currentMonthStr }}</view>
      <view class="calendar-header-btn" @click="nextMonth">
        <text>下一月</text>
      </view>
      <view class="calendar-header-btn calendar-header-btn-today" @click="today">
        <text>今天</text>
      </view>
    </view>
    <view class="calendar-week">
      <view v-for="week in weekDays" :key="week" class="calendar-week-item">{{
        week
      }}</view>
    </view>
    <view class="calendar-body">
      <view
        v-for="(day, index) in calendarDays"
        :key="index"
        class="calendar-day"
        :class="{
          'calendar-day-other': !day.currentMonth,
          'calendar-day-today': day.isToday,
          'calendar-day-marked': markedDays.includes(day.date),
        }"
        @click="onDayClick(day)"
      >
        <view class="calendar-day-number">{{ day.dayNumber }}</view>
        <view
          v-if="markedDays.includes(day.date)"
          class="calendar-day-dot"
        ></view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";

const props = defineProps<{
  markedDays?: string[]; // 格式：['2024-03-15', '2024-03-20']
}>();

const emit = defineEmits<{
  (e: "dayClick", date: string): void;
  (e: "monthChange", year_month: string): void;
}>();

const weekDays = ["一", "二", "三", "四", "五", "六", "日"];
const currentDate = new Date();
const currentYear = ref(currentDate.getFullYear());
const currentMonth = ref(currentDate.getMonth());

const markedDays = computed(() => props.markedDays || []);

const calendarDays = computed(() => {
  const year = currentYear.value;
  const month = currentMonth.value;
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);
  const days = [];

  // 填充上个月的日期
  const firstDayWeek = firstDay.getDay() || 7; // 将周日的0转换为7
  for (let i = firstDayWeek - 1; i > 0; i--) {
    const date = new Date(year, month, -i);
    days.push({
      date: formatDate(date),
      dayNumber: date.getDate(),
      currentMonth: false,
      isToday: isToday(date),
    });
  }

  // 填充当前月的日期
  for (let i = 1; i <= lastDay.getDate(); i++) {
    const date = new Date(year, month, i);
    days.push({
      date: formatDate(date),
      dayNumber: i,
      currentMonth: true,
      isToday: isToday(date),
    });
  }

  // 填充下个月的日期
  const remainingDays = 42 - days.length;
  for (let i = 1; i <= remainingDays; i++) {
    const date = new Date(year, month + 1, i);
    days.push({
      date: formatDate(date),
      dayNumber: date.getDate(),
      currentMonth: false,
      isToday: isToday(date),
    });
  }

  return days;
});

const formatDate = (date: Date): string => {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")}`;
};

const isToday = (date: Date): boolean => {
  const today = new Date();
  return (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  );
};

const prevMonth = () => {
  if (currentMonth.value === 0) {
    currentYear.value--;
    currentMonth.value = 11;
  } else {
    currentMonth.value--;
  }
  emit(
    "monthChange",
    `${currentYear.value}-${String(currentMonth.value + 1).padStart(2, "0")}`
  );
};

const nextMonth = () => {
  if (currentMonth.value === 11) {
    currentYear.value++;
    currentMonth.value = 0;
  } else {
    currentMonth.value++;
  }
  emit(
    "monthChange",
    `${currentYear.value}-${String(currentMonth.value + 1).padStart(2, "0")}`
  );
};

const onDayClick = (day: any) => {
  // 根据点击日期与当前月份判断是否需要翻月
  const currentMonthStr = `${String(currentMonth.value + 1).padStart(2, "0")}`;
  const clickMonthStr = day.date.split("-")[1];
  if (clickMonthStr > currentMonthStr) {
    nextMonth();
  } else if (clickMonthStr < currentMonthStr) {
    prevMonth();
  }

  emit("dayClick", day);
};

const today = () => {
  currentYear.value = currentDate.getFullYear();
  currentMonth.value = currentDate.getMonth();
  emit(
    "monthChange",
    `${currentYear.value}-${String(currentMonth.value + 1).padStart(2, "0")}`
  );
};
// 当前月份字符串添加0
const currentMonthStr = computed(() => {
  return `${currentYear.value}/${String(currentMonth.value + 1).padStart(
    2,
    "0"
  )}`;
});
</script>

<style scoped lang="scss">
.calendar {
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;

    &-title {
      font-size: 32rpx;
      font-weight: bold;
    }

    &-btn {
      padding: 12rpx 16rpx;
      color: var(--primary-color);
      border: 1rpx solid var(--primary-color);
      border-radius: 10rpx;
      font-size: 24rpx;
      &-today {
        color: var(--warning-color);
        border: 1rpx solid var(--warning-color);
      }
    }
  }

  &-week {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    text-align: center;
    padding: 20rpx 0;
    background-color: #efefef;

    &-item {
      font-size: 28rpx;
      color: #999;
    }
  }

  &-body {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 10rpx;
    padding: 20rpx 0;
  }

  &-day {
    width: 80rpx;
    position: relative;
    height: 80rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    &-number {
      font-size: 28rpx;
      color: #333;
    }

    &-other {
      .calendar-day-number {
        color: #D4D4D4;
      }
    }

    &-today {
      .calendar-day-number {
        color: var(--primary-color);
        font-weight: bold;
      }
    }

    &-marked {
      border-radius: 50%;
      background: var(--primary-color);
      .calendar-day-number {
        color: #fff;
      }

      .calendar-day-dot {
        position: absolute;
        top: 4rpx;
        right: 4rpx;
        width: 12rpx;
        height: 12rpx;
        background: var(--danger-color);
        border-radius: 50%;
      }
    }
  }
}
</style>
