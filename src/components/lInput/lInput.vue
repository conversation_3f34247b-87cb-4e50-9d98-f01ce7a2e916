<template>
  <view :class="['input-view', props.type, props.shape]">
    <image class="input-icon" :src="currentImg" mode="aspectFit"></image>
    <input
      :placeholder="currentPlace"
      v-model="inputValue"
      placeholder-class="input-placeholder"
      class="input"
      :maxlength="props.type === 'phone' ? 11 : undefined"
      :type="currentType"
    />
    <view
      class="code-view"
      v-if="props.type === 'code'"
      @click="startCodeTimer"
      :class="{ disabled: timer || props.disabled }"
    >
      {{ codeText }}
    </view>
  </view>
</template>

<script setup lang="ts" name="lInput">
import { computed, ref } from "vue";
import { importFile } from "@/utils/file";

interface Props {
  type?: "phone" | "code" | "pass";
  shape?: "white" | "black";
  modelValue?: string;
  placeholder?: string;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  type: "code",
  shape: "white",
  modelValue: "",
  placeholder: "",
  disabled: false,
});

const emit = defineEmits<{
  (e: "code-click"): void;
  (e: "update:modelValue", value: string): void;
}>();

// 验证码60秒倒计时函数
const codeTime = ref(60);
const timer = ref<ReturnType<typeof setInterval>>();
const codeText = computed(() => {
  if (timer.value) {
    return `${codeTime.value}s`;
  }
  return "获取验证码";
});

// 使用computed实现v-model
const inputValue = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit("update:modelValue", value);
  },
});
const startCodeTimer = () => {
  if (timer.value || props.disabled) return;
  codeTime.value = 60;
  emit("code-click");
  timer.value = setInterval(() => {
    if (codeTime.value <= 0) {
      clearInterval(timer.value);
      timer.value = undefined;
      return;
    }
    codeTime.value--;
  }, 1000);
};

const currentImg = computed(() => {
  const url = "../static/images/login/" + props.type + props.shape + ".png";
  return importFile(url);
});

const currentType = computed(() => {
  if (props.type === "phone") {
    return "number";
  }
  if (props.type === "code") {
    return "number";
  }
  if (props.type === "pass") {
    return "password";
  }
});

const currentPlace = computed(() => {
  const obj = {
    pass: "密码",
    code: "验证码",
    phone: "手机号",
  };
  return props.placeholder || "请输入" + obj[props.type];
});
</script>

<style scoped lang="less">
.input-view {
  height: 14vw;
  // border: 0.27vw solid #fff;
  border-radius: 4.67vw;
  display: flex;
  align-items: center;
  padding: 0 4.27vw 0 7.2vw;

  &.white {
    border: 0.27vw solid #fff;
    padding-right: 16.2vw;
    .input {
      color: #fff;
      text-align: center;
    }
    .input-placeholder {
      color: #f8fbff !important;
    }
    .input-icon {
      margin-right: 0;
    }
  }

  &.black {
    border: 0.27vw solid #ebebeb;

    .input {
      color: #333;
      .input-placeholder {
        color: #ebebeb !important;
      }
    }
  }

  .input {
    flex: 1;
    font-size: 3.2vw;
  }

  .input-icon {
    width: 3.47vw;
    height: 4.13vw;
    flex-shrink: 0;
    margin-right: 6.2vw;
  }

  .code-view {
    width: 20.8vw;
    height: 7.6vw;
    background-color: #82bbff;
    border-radius: 1.3vw;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    color: #fff;
    font-size: 20rpx;
    &.disabled {
      background-color: #bbbbbb;
    }
  }
}
</style>
