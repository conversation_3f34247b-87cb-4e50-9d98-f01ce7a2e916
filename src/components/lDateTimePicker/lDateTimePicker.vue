<template>
  <view class="datetime-picker">
    <view class="picker-header" @click="showPicker">
      <text
        class="picker-value"
        :class="{ 'picker-value-placeholder': !displayValue }"
        >{{ displayValue || placeholder }}</text
      >
      <uni-icons class="plan-row-value-icon" type="right" size="15"></uni-icons>
    </view>

    <uni-popup ref="popup" type="bottom">
      <view class="picker-popup">
        <view class="picker-popup-header">
          <text class="picker-popup-cancel" @click="cancel">取消</text>
          <text class="picker-popup-title">{{ title }}</text>
          <text class="picker-popup-confirm" @click="confirm">确定</text>
        </view>

        <picker-view
          :indicator-style="indicatorStyle"
          :value="pickerValue"
          @change="onChange"
          class="picker-view"
        >
          <picker-view-column>
            <view v-for="year in years" :key="year" class="picker-item">
              {{ year }}年
            </view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="month in months" :key="month" class="picker-item">
              {{ month }}月
            </view>
          </picker-view-column>
          <picker-view-column>
            <view v-for="day in days" :key="day" class="picker-item">
              {{ day }}日
            </view>
          </picker-view-column>
          <picker-view-column v-if="showTime">
            <view v-for="hour in hours" :key="hour" class="picker-item">
              {{ hour }}时
            </view>
          </picker-view-column>
          <picker-view-column v-if="showTime">
            <view v-for="minute in minutes" :key="minute" class="picker-item">
              {{ minute }}分
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";

const props = defineProps<{
  modelValue?: string;
  title?: string;
  showTime?: boolean;
  minDate?: string;
  maxDate?: string;
  placeholder?: string;
}>();

const emit = defineEmits<{
  (e: "update:modelValue", value: string): void;
  (e: "change", value: string): void;
}>();
const currentDate = new Date();

// 默认值从当前日期开始禁止选择当前日期以前
const pickerValue = ref([
  0,
  currentDate.getMonth(),
  currentDate.getDate() - 1,
  currentDate.getHours(),
  currentDate.getMinutes(),
]);
const defaultPlaceholder = "请选择日期";

const updatePickerValue = () => {
  const yearIndex = pickerValue.value[0];
  const monthIndex = pickerValue.value[1];
  const dayIndex = pickerValue.value[2];
  const hourIndex = pickerValue.value[3];
  const minuteIndex = pickerValue.value[4];

  const selectedYear = years.value[yearIndex];
  const selectedMonth = months.value[monthIndex];
  const selectedDay = days.value[dayIndex];
  const selectedHour = hours.value[hourIndex];
  const selectedMinute = minutes.value[minuteIndex];

  const selectedDate = new Date(
    selectedYear,
    selectedMonth - 1,
    selectedDay,
    selectedHour,
    selectedMinute,
    0 // 添加秒，默认为0
  );

  if (selectedDate < minDate.value) {
    pickerValue.value = [
      0,
      currentDate.getMonth(),
      currentDate.getDate() - 1,
      currentDate.getHours(),
      currentDate.getMinutes(),
    ];
  }
};

watch(pickerValue, updatePickerValue);

const defaultTitle = "选择日期时间";
const defaultMinDate = `${currentDate.getFullYear()}-${
  currentDate.getMonth() + 1
}-${currentDate.getDate()}`;
const defaultMaxDate = `${currentDate.getFullYear() + 10}-${
  currentDate.getMonth() + 1
}-${currentDate.getDate()}`;

// 计算属性
const title = computed(() => props.title || defaultTitle);
const minDate = computed(() => new Date(props.minDate || defaultMinDate));
const maxDate = computed(() => new Date(props.maxDate || defaultMaxDate));
const placeholder = computed(() => props.placeholder || defaultPlaceholder);
// 选择器样式
const indicatorStyle = "height: 50px;";

// 数据源
const years = computed(() => {
  const yearList = [];
  for (
    let i = minDate.value.getFullYear();
    i <= maxDate.value.getFullYear();
    i++
  ) {
    yearList.push(i);
  }
  return yearList;
});

const months = computed(() => {
  return Array.from({ length: 12 }, (_, i) => i + 1);
});

const days = computed(() => {
  const year = years.value[pickerValue.value[0]];
  const month = months.value[pickerValue.value[1]];

  // 计算该月的天数
  let daysInMonth;
  if (month === 2) {
    // 2月份特殊处理，判断是否闰年
    daysInMonth =
      (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0 ? 29 : 28;
  } else if ([4, 6, 9, 11].includes(month)) {
    // 4、6、9、11月是30天
    daysInMonth = 30;
  } else {
    // 其他月份是31天
    daysInMonth = 31;
  }
  return Array.from({ length: daysInMonth }, (_, i) => i + 1);
});

const hours = computed(() => {
  return Array.from({ length: 24 }, (_, i) => i);
});

const minutes = computed(() => {
  return Array.from({ length: 60 }, (_, i) => i);
});

// 当前选中的值

// 显示的值
const displayValue = computed(() => {
  if (!props.modelValue) return "";
  const date = new Date(props.modelValue);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hour = String(date.getHours()).padStart(2, "0");
  const minute = String(date.getMinutes()).padStart(2, "0");

  if (props.showTime) {
    return `${year}-${month}-${day} ${hour}:${minute}:00`;
  }
  return `${year}-${month}-${day}`;
});

// 弹出层引用
const popup = ref();

// 显示选择器
const showPicker = () => {
  if (props.modelValue) {
    const date = new Date(props.modelValue);
    pickerValue.value = [
      years.value.indexOf(date.getFullYear()),
      date.getMonth(),
      date.getDate() - 1,
      date.getHours(),
      date.getMinutes(),
    ];
  }
  popup.value.open();
};

// 取消选择
const cancel = () => {
  popup.value.close();
};

// 确认选择
const confirm = () => {
  const year = years.value[pickerValue.value[0]];
  const month = String(months.value[pickerValue.value[1]]).padStart(2, "0");
  const day = String(days.value[pickerValue.value[2]]).padStart(2, "0");
  const hour = String(hours.value[pickerValue.value[3]]).padStart(2, "0");
  const minute = String(minutes.value[pickerValue.value[4]]).padStart(2, "0");
  // 验证日期是否有效
  const selectedDate = new Date(year, Number(month) - 1, Number(day));
  if (selectedDate.getDate() !== Number(day)) {
    // 如果日期无效，使用该月的最后一天
    const lastDay = new Date(year, Number(month), 0).getDate();
    pickerValue.value[2] = lastDay - 1;
    return confirm(); // 重新确认
  }

  let value = `${year}-${month}-${day}`;
  if (props.showTime) {
    value += ` ${hour}:${minute}:00`;
  }
  emit("update:modelValue", value);
  emit("change", value);
  popup.value.close();
};

// 选择改变
const onChange = (e: any) => {
  const newValue = e.detail.value;
  // 如果年月发生变化，重置日期选择
  if (
    newValue[0] !== pickerValue.value[0] ||
    newValue[1] !== pickerValue.value[1]
  ) {
    const year = years.value[newValue[0]];
    const month = months.value[newValue[1]];

    // 计算该月的天数
    let lastDay;
    if (month === 2) {
      lastDay =
        (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0 ? 29 : 28;
    } else if ([4, 6, 9, 11].includes(month)) {
      lastDay = 30;
    } else {
      lastDay = 31;
    }

    // 如果是当前月份，且年份是当前年份，则使用当前日期
    const isCurrentMonth =
      year === currentDate.getFullYear() &&
      month === currentDate.getMonth() + 1;
    const currentDay = currentDate.getDate();

    // 如果当前选择的日期大于新月份的天数，则设置为最后一天
    if (isCurrentMonth) {
      if (newValue[2] >= currentDay) {
        newValue[2] = currentDay - 1;
      }
    } else {
      if (newValue[2] >= lastDay) {
        newValue[2] = lastDay - 1;
      }
    }
  }
  pickerValue.value = newValue;
};
</script>

<style scoped lang="scss">
.datetime-picker {
  width: 100%;

  .picker-header {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 8rpx;

    .picker-value {
      flex: 1;
      font-size: 3.47vw;
      color: #333333;
      margin-right: 1.33vw;
    }
    .picker-value-placeholder {
      font-size: 3.47vw;
      color: #909090;
    }
  }

  .picker-popup {
    background-color: #fff;
    border-radius: 24rpx 24rpx 0 0;

    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 30rpx;
      border-bottom: 1rpx solid #eee;

      .picker-popup-cancel {
        font-size: 28rpx;
        color: #999;
      }

      .picker-popup-title {
        font-size: 32rpx;
        color: #333;
        font-weight: bold;
      }

      .picker-popup-confirm {
        font-size: 28rpx;
        color: var(--primary-color);
      }
    }
  }

  .picker-view {
    width: 100%;
    height: 400rpx;

    .picker-item {
      line-height: 50px;
      text-align: center;
      font-size: 28rpx;
      color: #333;
    }
  }
}
</style>
