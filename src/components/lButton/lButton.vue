<template>
  <view>
    <!-- 写一个圆角按钮支持文字插槽 -->
    <button class="l-button">
      <slot></slot>
    </button>
  </view>
</template>

<script setup lang="ts" name="lButton"></script>

<style scoped lang="less">
.l-button {
  width: 100%;
  height: 14vw;
  background-color: #1f8bf8;
  color: #fff;
  border-radius: 4.67vw;
  box-shadow: 0 0.53vw 1.07vw 0 #507aef;
  font-size: 5.33vw;
  letter-spacing: 5.6vw;
  text-align: center;
  line-height: 14vw;
  padding-left: 6.2vw;
}
</style>
