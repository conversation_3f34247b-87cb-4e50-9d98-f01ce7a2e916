<template>
  <view class="l-cascader">
    <uv-picker
      ref="picker"
      :columns="addressList"
      :loading="loading"
      keyName="region_name"
      @change="change"
      @confirm="confirm"
    >
    </uv-picker>
    <view class="l-cascader-input">
      <view class="l-cascader-input-text" @tap="openPicker">
        {{ currentValue ? currentValue : "请选择" }}
      </view>
      <view class="l-cascader-input-icon">
        <uni-icons
          @click="handleLocation"
          class="location-icon"
          type="location"
          size="16"
          color="#086bdb"
        />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { getAreaData, getLocationInfo } from "@/api/login";
import type { Response } from "@/utils/type";

interface Area {
  id: number;
  region_name: string;
}

const picker = ref();
const loading = ref(false);
const provinceList = ref<Area[]>([]);
const cityList = ref<Area[]>([]);
const areaList = ref<Area[]>([]);
const currentValue = ref("");

const props = defineProps({
  modelValue: {
    type: Array,
    default: [],
  },
});
const emit = defineEmits(["update:modelValue"]);

const change = async (e: any) => {
  const { columnIndex, index, indexs } = e;
  console.log(e);
  if (columnIndex === 0) {
    cityList.value = [];
    areaList.value = [];
    const pid = provinceList.value[index].id;
    await getColumn(pid, "city");
    if (cityList.value.length > 0) {
      const cid = cityList.value[index].id;
      await getColumn(cid, "area");
    }
    picker.value.setIndexs([index, 0, 0], true);
  } else if (columnIndex === 1) {
    areaList.value = [];
    if (cityList.value.length > 0) {
      const cid = cityList.value[index].id;
      await getColumn(cid, "area");
      picker.value.setIndexs(indexs, true);
    }
  }
};
const getColumn = async (pid: number, type: string) => {
  console.log("子数据触发", pid, type);

  loading.value = true;
  const res: Response = (await getAreaData({ pid })) as Response;
  if (res.code === 200) {
    if (type === "province") {
      provinceList.value = res.data;
    } else if (type === "city") {
      cityList.value = res.data;
    } else if (type === "area") {
      areaList.value = res.data;
    }
  }
  loading.value = false;
};
const openPicker = () => {
  picker.value.open();
};

const handleLocation = async () => {
  uni.getLocation({
    type: "wgs84",
    geocode: true,
    success: async (res) => {
      const { longitude, latitude } = res;
      console.log(longitude);
      if (longitude && latitude) {
        const {
          data: {
            province,
            city,
            district,
            address,
            province_id,
            city_id,
            district_id,
          },
          code,
        }: Response = (await getLocationInfo({
          lng: longitude,
          lat: latitude,
        })) as Response;
        if (code === 200) {
          currentValue.value = `${province} ${city} ${district}`;
          emit("update:modelValue", [
            province_id,
            city_id,
            district_id,
            address,
          ]);
        } else {
          uni.showToast({ title: "获取位置失败", icon: "none" });
        }
      }
    },
    fail: (err: any) => {
      uni.showToast({ title: "获取位置失败", icon: "none" });
    },
    complete: () => {
      console.log("complete");
    },
  });
};
const addressList = computed(() => {
  return [provinceList.value, cityList.value, areaList.value];
});
const confirm = (e: any) => {
  const { value } = e;
  const array = value.map((item: Area) => item.id);
  currentValue.value = value.map((item: Area) => item.region_name).join(" ");
  emit("update:modelValue", array);
};
onMounted(async () => {
  await getColumn(0, "province");
  if (provinceList.value.length > 0) {
    picker.value.setIndexs([0, 0, 0], true);
    change({ columnIndex: 0, index: 0, indexs: [0, 0, 0] });
  }
});
</script>

<style lang="scss" scoped>
.l-cascader {
  flex: 1;
  .l-cascader-input {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #1a1a1a;
    font-size: 24rpx;
  }
}
</style>
