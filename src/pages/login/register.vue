<template>
  <view class="register">
    <lCard class="register-card">
      <view class="register-title">
        <text>{{ title }}</text>
      </view>
      <form>
        <lInput
          name="phone"
          type="phone"
          shape="black"
          v-model="form.phone"
          placeholder="请输入手机号"
          class="register-input"
        />
        <lInput
          name="code"
          type="code"
          shape="black"
          v-model="form.code"
          :disabled="disabled"
          @code-click="getCode"
          placeholder="请输入验证码"
          class="register-input"
        />
        <lInput
          name="password"
          type="pass"
          shape="black"
          v-model="form.password"
          placeholder="请输入密码"
          class="register-input"
        />
        <lInput
          name="confirm_password"
          type="pass"
          shape="black"
          v-model="form.confirm_password"
          placeholder="请重复密码"
          class="register-input"
        />
        <lButton
          type="primary"
          @click="formSubmit"
          formType="submit"
          class="register-button"
        >
          确定
        </lButton>
      </form>
    </lCard>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { register, getCodeApi } from "@/api/login";
import type { Response } from "@/utils/type";
const pageType = ref("register");
const form = ref({
  phone: "",
  code: "",
  password: "",
  confirm_password: "",
});
const formSubmit = async (e: any) => {
  if (
    !form.value.phone ||
    !form.value.code ||
    !form.value.password ||
    !form.value.confirm_password
  ) {
    return uni.showToast({
      title: "请填写所有必填字段",
      icon: "none",
    });
  }
  if (form.value.password !== form.value.confirm_password) {
    return uni.showToast({
      title: "两次输入的密码不一致",
      icon: "none",
    });
  }
  const params = {
    phone: form.value.phone,
    code: form.value.code,
    password: form.value.password,
    confirm_password: form.value.confirm_password,
    sms_type: pageType.value,
  };
  let { code }: Response = (await register(params)) as Response;
  if (code === 200) {
    uni.showToast({
      title: "提交成功",
      icon: "success",
    });
    uni.navigateTo({
      url: "/pages/login/login",
    });
  }
};
onLoad((options) => {
  if (options && options.code) {
    pageType.value = options.code;
  }
});

// 根据手机号是否符合手机号格式禁用获取验证码按钮
const disabled = computed(() => {
  // 手机正则
  const phoneReg = /^1[3-9]\d{9}$/;
  return !form.value.phone || !phoneReg.test(form.value.phone);
});

const title = computed(() => {
  return pageType.value === "register_phone" ? "使用手机号注册" : "找回重置密码";
});

// 获取验证码
const getCode = async () => {
  let { code }: Response = (await getCodeApi({
    phone: form.value.phone,
    //验证码类型 login：登录；register_phone：注册；binding：绑定手机号；change_pwd：修改密码；change_phone：修改手机号
    sms_type: pageType.value,
  })) as Response;
  if (code === 200) {
    uni.showToast({
      title: "获取验证码成功",
      icon: "success",
    });
  }
};
</script>

<style lang="less" scoped>
.register {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6.8vw;
  box-sizing: border-box;
  background: radial-gradient(
    100% 120% at 100% 100%,
    #88beff 10%,
    #61abff 49%,
    #5cc7ff 100%
  );
  .register-title {
    font-size: 4.8vw;
    // font-weight: bold;
    color: #000000;
    text-align: center;
    margin-bottom: 6.8vw;
  }
  .register-input {
    margin-bottom: 3.73vw;
  }
  .register-button {
    margin-top: 6.8vw;
  }
  .register-card {
    padding-top: 3.73vw;
    padding-bottom: 3.73vw;
    box-sizing: border-box;
  }
}
</style>
