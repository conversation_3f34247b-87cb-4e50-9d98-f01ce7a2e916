<template>
  <view class="register">
    <lCard class="register-card">
      <view class="register-title">
        <text>为了账号安全，需验证(绑定)手机号</text>
      </view>
      <form>
        <lInput
          name="phone"
          type="phone"
          shape="black"
          v-model="form.phone"
          placeholder="请输入手机号"
          class="register-input"
        />
        <lInput
          name="code"
          type="code"
          shape="black"
          v-model="form.code"
          placeholder="请输入验证码"
          class="register-input"
          :disabled="disabled"
        />

        <lButton
          type="primary"
          @click="formSubmit"
          formType="submit"
          class="register-button"
        >
          确定
        </lButton>
      </form>
    </lCard>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { loginByCode } from "@/api/login";
import type { Response } from "@/utils/type";
const form = ref({
  phone: "",
  code: "",
});
const formSubmit = async (e: any) => {
  if (!form.value.phone || !form.value.code) {
    return uni.showToast({
      title: "请填写所有必填字段",
      icon: "none",
    });
  }
  const params = {
    phone: form.value.phone,
    code: form.value.code,
    sms_type: "login",
  };
  let { data, code }: Response = (await loginByCode(params)) as Response;
  if (code === 200) {
    uni.setStorageSync("store", data);
    uni.setStorageSync("token", data.token);
    uni.switchTab({
      url: "/pages/home/<USER>",
    });
  }
};
// 根据手机号是否符合手机号格式禁用获取验证码按钮
const disabled = computed(() => {
  // 手机正则
  const phoneReg = /^1[3-9]\d{9}$/;
  return !form.value.phone || !phoneReg.test(form.value.phone);
});
</script>

<style lang="less" scoped>
.register {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6.8vw;
  box-sizing: border-box;
  background: radial-gradient(
    100% 120% at 100% 100%,
    #88beff 10%,
    #61abff 49%,
    #5cc7ff 100%
  );
  .register-title {
    font-size: 4.8vw;
    // font-weight: bold;
    color: #000000;
    text-align: center;
    margin-bottom: 6.8vw;
  }
  .register-input {
    margin-bottom: 3.73vw;
  }
  .register-button {
    margin-top: 6.8vw;
  }
  .register-card {
    padding-top: 3.73vw;
    padding-bottom: 3.73vw;
    box-sizing: border-box;
  }
}
</style>
