<template>
  <uni-nav-bar
    :border="false"
    title="套餐管理"
    color="#fff"
    left-icon="left"
    right-text="新增"
    @clickRight="handleClickRight"
    @clickLeft="handleClickLeft"
    backgroundColor="var(--primary-color)"
  ></uni-nav-bar>
  <scroll-view class="meal-manage" scroll-y>
    <view class="meal-top">
      <uni-easyinput
        class="search-input"
        suffixIcon="search"
        v-model="value"
        placeholder="请输入车牌、客户姓名、手机号码查询"
        @iconClick="iconClick"
      ></uni-easyinput>
      <uni-segmented-control
        :current="current"
        :values="items"
        @clickItem="onClickItem"
        styleType="text"
        activeColor="var(--warning-color)"
      ></uni-segmented-control>
    </view>
    <view class="tips-text"
      >向左滑动可以对有效套餐进行
      <text class="color1">作废</text>
    </view>
    <uni-swipe-action v-if="current === 0">
      <!-- 使用插槽 （请自行给定插槽内容宽度）-->
      <uni-swipe-action-item
        style="margin-top: 3.2vw"
        v-for="item in packageList"
        :key="item.id"
      >
        <view class="meal-list-item" @tap="handleClickDetail({ id: 1 })">
          <view class="item-row flex-sbw">
            <view class="meal-item-name">{{ item.title }}</view>
            <view
              >剩余到期：<text class="color4">{{
                item.effective_end_date
              }}</text
              >天</view
            >
          </view>
          <view class="row-content">套餐内容：{{ item.content }}</view>
          <view class="item-row flex-sbw">
            <view
              >剩余：<text class="color3"
                >{{ item.postnum - item.buy_num }} </text
              >张</view
            >
            <view
              >已购卡：<text class="color3">{{ item.buy_num }} </text>张</view
            >
            <view
              >卡单价：<text class="color1">{{ item.price }} </text>元</view
            >
          </view>
        </view>
        <template #right>
          <view
            class="right-item flex-center"
            @click.native="openDialog(item, 0)"
          >
            <image
              src="@/static/images/warehouse/cancel.png"
              mode="aspectFit"
            />
            作<br />废
          </view>
        </template>
      </uni-swipe-action-item>
    </uni-swipe-action>
    <uni-swipe-action v-else-if="current === 1">
      <!-- 使用插槽 （请自行给定插槽内容宽度）-->
      <uni-swipe-action-item
        style="margin-top: 3.2vw"
        v-for="item in vehicleList"
        :key="item.id"
      >
        <view class="meal-list-item">
          <view class="item-row flex-sbw">
            <view class="user-item-name">{{
              item.customer_info.fullname
            }}</view>
            <view>{{ item.customer_info.phone }}</view>
            <view class="color2">{{
              item.thetype === 1 ? "次卡套餐" : "套餐"
            }}</view>
          </view>
          <view class="item-row flex-sbw">
            <view
              >所属车牌：<text class="color4">{{
                item.car_info.car_license_code
              }}</text></view
            >
            <view
              >套餐名称：<text class="color4">{{ item.title }}</text></view
            >
          </view>
          <view class="item-row flex-sbw">
            <view
              >开卡时间：<text class="color3">{{
                item.effective_begin_date
              }}</text></view
            >
            <view
              >剩余有效期：<text class="color4">{{
                item.effective_end_date
              }}</text
              >天</view
            >
          </view>
        </view>
        <template #right>
          <view class="right-item flex-center" @touchend="openDialog(item, 1)">
            <image
              src="@/static/images/warehouse/back_vip.png"
              mode="aspectFit"
            />
            退<br />卡
          </view>
        </template>
      </uni-swipe-action-item>
    </uni-swipe-action>
    <view class="expire-list" style="padding-bottom: 10vw" v-else>
      <view
        style="margin-top: 3.2vw"
        @tap="handleClickDetail(item)"
        class="meal-list-item"
        v-for="item in packageList"
        :key="item.id"
      >
        <view class="item-row flex-sbw">
          <view class="expire-item-name">套餐名称{{ item.title }}</view>
          <view class="color4">已作废</view>
        </view>
        <view class="row-content">套餐内容：{{ item.content }}</view>
        <view class="item-row flex-sbw">
          <view
            >作废时间：<text class="color4">{{ item.tovoid_time }}</text></view
          >
          <view
            >已购卡：<text style="color: #404040">{{ item.buy_num }}</text
            >张</view
          >
        </view>
      </view>
    </view>
    <uni-popup ref="popup" type="dialog" :mask-click="false">
      <uni-popup-dialog
        type="input"
        v-model="note"
        :title="iaCancel ? '请输入作废理由' : '请输入退卡理由'"
        @confirm="handleClickConfirm"
      ></uni-popup-dialog>
    </uni-popup>
  </scroll-view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import {
  getPackageList,
  cancelPackage,
  getVehiclePackageList,
  refundPackage,
} from "@/api/package";
import type { TableData } from "@/utils/type";
import type { UniSegmentedControl, UniPopup } from "uni-ui-types";

interface PackageItem {
  id: number; // 套餐ID
  title: string; // 套餐名称
  thetype: number; // 套餐类型 1次卡 2套餐 3折扣 4储值
  effective_type: number; // 有效期类型  1固定时间段 2一直有效  3固定时长
  num_type: number; // 次数类型 1固定次数 2不限制次数
  effective_begin_date: string; // 开始日期
  effective_end_date: string; // 截止日期
  num: number; // 次数
  discount: number; // 折扣 按百分比 0~100
  content: string; // 服务内容
  price: string; // 单价
  postnum: number; // 发行数量 单位张
  buy_num: number; // 已购套餐数量
  is_tovoid: number; // 是否作废 0否 1是
  tovoid_time: null | string; // 作废时间
  tovoid_note: string; // 作废备注
  tovoid_user_id: number; // 作废人ID，0表示系统自动完成
  effective_date_type: null | number; // 固定时常有效期类型  1:6月起，2:1年期，3:2年期
}

interface VehiclePackageItem {
  id: number; // 已拥有套餐ID
  company_id: number;
  store_id: number; // 门店ID
  customer_id: number; // 客户ID
  car_id: number; // 车辆Id
  package_id: number; // 套餐ID
  package_code: string; // 套餐编码
  title: string; // 套餐名称
  thetype: number; // 类型 1次卡（有次数限制） 2套餐（无次数限制） 3折扣（无次数限制）
  effective_type: number; // 有效期类型  1固定时间段 2一直有效 3固定期限
  num_type: number; // 次数类型 1固定次数 2不限制次数
  effective_begin_date: string; // 开始日期
  effective_end_date: string; // 截止日期
  all_num: number; // 次卡 总次数
  used_num: number; // 次卡 已使用次数
  surplus_num: number; // 次卡 剩余次数
  discount: number; // 折扣 按百分比 0~100
  content: string; // 服务内容
  is_tovoid: number; // 是否作废 0否 1是
  tovoid_time: null | string; // 作废时间
  tovoid_note: string; // 作废备注
  tovoid_user_id: number; // 作废人  0表示系统自动完成
  is_return: number; // 是否退卡，0否，1是
  return_time: null | string; // 退卡时间
  return_user_id: null | number; // 退卡人
  price: string; // 储值金额
  used_price: string; // 已使用储值金额
  surplus_price: string; // 剩余储值金额
  customer_info: {
    // 客户信息
    id: number; // 客户ID
    fullname: string; // 客户名称
    phone: string; // 客户电话
  };
  car_info: {
    // 车辆信息
    id: number; // 车辆ID
    car_license_code: string; // 车牌号
  };
}

interface ApiResponse<T> {
  code: number;
  data: T;
  message?: string;
}

interface PackageListResponse {
  list: PackageItem[];
  count: number;
}

const note = ref("");
const packageList = ref<PackageItem[]>([]);
const vehicleList = ref<VehiclePackageItem[]>([]);
const value = ref("");
const current = ref(0);
const items = ref(["门店套餐", "开通记录", "过期作废"]);
const popup = ref<UniPopup>();
const cancelItem = ref<VehiclePackageItem | PackageItem>();
const iaCancel = ref(false);
const iconClick = () => {
  console.log("iconClick");
};
const handleClickLeft = () => {
  uni.navigateBack();
};

const handleClickRight = () => {
  uni.navigateTo({
    url: "/pages/home/<USER>",
  });
};
const onClickItem: UniSegmentedControl["onClickItem"] = ({
  currentIndex,
}: {
  currentIndex: number;
}) => {
  current.value = currentIndex;
  if (currentIndex === 0) {
    getList();
  } else if (currentIndex === 1) {
    getVehicleList();
  } else {
    getList();
  }
};
const handleClickConfirm = () => {
  if (iaCancel.value) {
    // 作废
    console.log("作废");
    cancelHandle(cancelItem.value as PackageItem);
  } else {
    // 退卡
    console.log("退卡");
    refundHandle(cancelItem.value as VehiclePackageItem);
  }
};
const handleClickDetail = (item: any) => {
  uni.navigateTo({
    url: "/pages/home/<USER>" + item.id,
    /* params: {
      item,
    }, */
  });
};

// 获取套餐列表
const getList = async () => {
  try {
    const params = {
      page: 1,
      limit: 10,
      thetype_show: current.value === 0 ? 1 : 2,
    };
    const res = (await getPackageList(
      params
    )) as ApiResponse<PackageListResponse>;
    if (res.code === 200) {
      packageList.value = res.data.list;
    }
  } catch (error) {
    console.error("获取套餐列表失败:", error);
  }
};

// 退卡
const refundHandle = async (item: VehiclePackageItem) => {
  try {
    const params = {
      package_id: item.id, // 套餐id
      customer_id: item.customer_id, // 客户id 开通折扣卡只需要客户id
    };
    const res = (await refundPackage(params)) as ApiResponse<any>;
    if (res.code === 200) {
      uni.showToast({ title: "退卡成功", icon: "success" });
    }
  } catch (error) {
    console.error("退卡失败:", error);
  }
};

const cancelHandle = async (item: PackageItem) => {
  try {
    const params = {
      id: item.id, // 套餐id
      tovoid_note: note.value, // 作废理由
    };
    const res = (await cancelPackage(params)) as ApiResponse<any>;
    if (res.code === 200) {
      uni.showToast({ title: "作废成功", icon: "success" });
    }
  } catch (error) {
    console.error("作废失败:", error);
  }
};
const openDialog = (item: any, type: number) => {
  cancelItem.value = item;
  iaCancel.value = type === 0;
  popup.value?.open();
};

// 获取车辆套餐列表
const getVehicleList = async () => {
  try {
    const params = {
      page: 1,
      limit: 10,
      package_id: 0,
      car_id: 0,
    };
    const res = (await getVehiclePackageList(params)) as ApiResponse<any>;
    if (res.code === 200) {
      vehicleList.value = res.data.list;
    }
  } catch (error) {
    console.error("获取车辆套餐列表失败:", error);
  }
};
onMounted(() => {
  getList();
});
</script>
<style scoped lang="scss">
.color1 {
  color: var(--warning-color);
}
.color2 {
  color: var(--success-color);
}
.color3 {
  color: var(--danger-color);
}
.color4 {
  color: var(--primary-color);
}
.meal-manage {
  height: calc(100% - 44px);
  box-sizing: border-box;
  background-color: #f2f2f2;
  .meal-top {
    padding: 3.5vw;
    background-color: #fff;
    .search-input {
      margin-bottom: 3.5vw;
    }
  }
  .tips-text {
    padding-top: 3.2vw;
    text-align: center;
    font-size: 3.47vw;
    color: #8f8e8e;
  }
  .meal-list-item {
    font-size: 3.47vw;
    color: #8f8e8e;
    padding: 1.5vw;
    background-color: #fff;
    width: 92.8vw;
    margin: 0 auto;
    border-radius: 0.67vw;
    .item-row {
      height: 10vw;
      .meal-item-name {
        color: #fff;
        font-size: 3.47vw;
        border-radius: 0.67vw;
        width: 45vw;
        height: 8.8vw;
        padding: 0 2.4vw;
        box-sizing: border-box;
        line-height: 8.8vw;
        text-align: center;
        background-color: var(--primary-color);
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      .user-item-name {
        width: 32vw;
        height: 8.8vw;
        padding: 0 2.4vw;
        line-height: 8.8vw;
        text-align: center;
        box-sizing: border-box;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        font-size: 3.47vw;
        border-radius: 0.67vw;
        color: var(--primary-color);
        border: 1px solid var(--primary-color);
      }
      .expire-item-name {
        width: 45vw;
        height: 8.8vw;
        padding: 0 2.4vw;
        line-height: 8.8vw;
        text-align: center;
        box-sizing: border-box;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        font-size: 3.47vw;
        border-radius: 0.67vw;
        color: #404040;
        border: 1px solid #404040;
      }
    }
    .row-content {
      width: 100%;
      height: 10vw;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      display: flex;
      align-items: center;
      flex-shrink: 0;
    }
  }
  .right-item {
    width: 19vw;
    height: 100%;
    background-color: var(--warning-color);
    color: #fff;
    font-size: 3.47vw;
    flex-direction: column;
    border-radius: 1.33vw;
    image {
      width: 4.53vw;
      height: 4.53vw;
      margin-bottom: 1.33vw;
    }
  }
}
</style>
