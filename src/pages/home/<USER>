<template>
  <uni-nav-bar
    :border="false"
    title="车店助手"
    color="#fff"
    backgroundColor="var(--primary-color)"
  ></uni-nav-bar>
  <view class="home">
    <!-- 顶部数据概览 -->
    <view class="reservation">
      <view class="reservation-header">
        <view class="time-tabs">
          <text class="time-tab">今日</text>
          <text class="time-tab">年度</text>
          <text class="time-tab">本月</text>
        </view>
        <view class="reservation-text" @tap="handleClickReservation">
          预约管理
        </view>
      </view>
      <view class="time-data-list">
        <view class="time-data-item">
          <view class="time-data-item-num flex-center">51</view>
          <view class="time-data-item-text">到店台次</view>
        </view>
        <view class="time-data-item">
          <view class="time-data-item-num flex-center">3123.50</view>
          <view class="time-data-item-text">到店产值 </view>
        </view>
        <view class="time-data-item">
          <view class="time-data-item-num flex-center">1</view>
          <view class="time-data-item-text">预约计划 </view>
        </view>
      </view>
    </view>
    <view class="billing">
      <view class="billing-open flex-center" @tap="showCover = true">
        <view class="order flex-center">
          <image src="@/static/images/home/<USER>" mode="aspectFit" />
        </view>
        <view class="billing-open-text"> 接车开单 </view>
      </view>
      <view class="billing-status">
        <view
          class="billing-status-item flex-center"
          v-for="item in statusList"
          :key="item.text"
        >
          <view class="status-icon">
            <view class="status-item-num flex-center">16</view>
            <image :src="item.icon" mode="aspectFit" />
          </view>
          <view class="billing-status-item-text">{{ item.text }}</view>
        </view>
      </view>
    </view>
    <view class="menu-list">
      <view
        class="menu-list-item flex-center"
        v-for="item in menuList"
        :key="item.text"
      >
        <view
          @tap="handleClickMenu(item.path)"
          class="menu-list-item-icon flex-center"
          :style="{ background: item.color }"
        >
          <image :src="item.icon" mode="aspectFit" />
        </view>
        <view class="menu-list-item-text">{{ item.text }}</view>
      </view>
    </view>
    <view class="menu-list">
      <view
        class="menu-list-item flex-center"
        v-for="item in platformList"
        :key="item.text"
      >
        <view @tap="handleClickMenu(item.path)" class="platform-list-item-icon">
          <image :src="item.icon" mode="aspectFit" />
        </view>
        <view class="menu-list-item-text">{{ item.text }}</view>
      </view>
    </view>
    <view class="page-footer">
      <text>版本号：2.0.0</text>
      <text>免费·易用·专业的汽修门店管理系统</text>
    </view>
    <cover-view class="cover-view" v-if="showCover">
      <cover-image
        class="cover-image"
        src="@/static/images/order/bgn41_1.png"
        mode="aspectFit"
        @tap="handleClickBilling(1)"
      />
      <cover-image
        class="cover-image"
        src="@/static/images/order/bgn41_2.png"
        mode="aspectFit"
        @tap="handleClickBilling(2)"
      />
      <cover-image
        class="cover-image"
        src="@/static/images/order/bgn41_3.png"
        mode="aspectFit"
        @tap="handleClickBilling(3)"
      />
    </cover-view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { importFile } from "@/utils/file";
const menuList = ref([
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "维修订单",
    color: "#6C5695",
    path: "/pages/order/orderList",
  },
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "会员管理",
    color: "#347BEF",
    path: "/pages/home/<USER>",
  },
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "套餐管理",
    color: "#B35CE5",
    path: "/pages/home/<USER>",
  },
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "记账管理",
    color: "#2BC662",
    path: "/pages/finance/account",
  },
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "服务配置",
    color: "#407B80",
    path: "/pages/service/index",
  },
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "库房管理",
    color: "#959595",
    path: "/pages/warehouse/warehouse",
  },
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "财务管理",
    color: "#C6A02B",
    path: "/pages/finance/finance",
  },
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "统计分析",
    color: "#955656",
    path: "/pages/finance/statistics",
  },
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "配件采购",
    color: "#4C8931",
    path: "/pages/warehouse/purchase",
  },
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "采购订单",
    color: "#897E31",
    path: "/pages/warehouse/purchaseOrder",
  },
]);
const platformList = ref([
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "资质认证",
    path: "/pages/home/<USER>",
  },
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "团队账户",
    path: "/pages/team/team",
  },
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "健康档案",
    path: "/pages/health/healthForm",
  },
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "平台服务",
    path: "/pages/home/<USER>",
  },
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "平台客服",
    path: "/pages/home/<USER>",
  },
]);
const statusList = ref([
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "已接车",
  },
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "待施工",
  },
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "返修单",
  },
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "待完工",
  },
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "待结算",
  },
  {
    icon: importFile("../static/images/home/<USER>"),
    text: "待提车",
  },
]);
const handleClickMenu = (path: string) => {
  uni.navigateTo({
    url: path,
    fail: (err) => {
      uni.showToast({
        title: "模块开发中",
        icon: "error",
      });
    },
  });
};
const showCover = ref(false);
const handleClickReservation = () => {
  uni.navigateTo({
    url: "/pages/client/plan",
  });
};
const handleClickBilling = (type: number) => {
  uni.navigateTo({
    url: `/pages/order/openOrder?type=${type}`,
    success: () => {
      showCover.value = false;
    },
  });
};
</script>

<style lang="less" scoped>
.home {
  width: 100%;
  height: 100%;
  background: #f6f6f6;
  overflow: auto;

  // uniappH5条件编译
  // #ifdef H5
  // #endif

  .reservation {
    width: 92vw;
    height: 33vw;
    background: #fff;
    border-radius: 2vw;
    margin: 0 auto;
    border-radius: 0 0 1.33vw 1.33vw;
    box-shadow: 0 0.27vw 0.67vw 0 #878787;
    .reservation-header {
      width: 100%;
      height: 10vw;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 5.4vw 3.47vw;
      font-size: 3.47vw;
      color: #333333;
      box-sizing: border-box;
      .time-tabs {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .time-tab {
          margin-right: 6.2vw;
          &.active {
            color: #ff7d37;
          }
        }
      }
    }
    .time-data-list {
      width: 100%;
      padding-top: 5.8vw;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .time-data-item {
        width: 33%;
        .time-data-item-num {
          font-size: 4vw;
          color: #f4691e;
          height: 6.8vw;
          border-right: 0.13vw solid #e2e2e2;
        }
        .time-data-item-text {
          font-size: 2.67vw;
          color: #333333;
          padding: 1.33vw 0;
          text-align: center;
        }
        &:last-child {
          .time-data-item-num {
            border-right: none;
          }
        }
      }
    }
  }
  .billing {
    width: 100%;
    background: #fff;
    display: flex;
    padding: 3.47vw 5.6vw;
    box-sizing: border-box;
    margin-top: 3.47vw;
    .billing-open {
      width: 40%;
      height: 32.13vw;
      flex-direction: column;
      .order {
        width: 11.73vw;
        height: 11.73vw;
        border-radius: 50%;
        background: #ff7d37;
        image {
          width: 6.4vw;
          height: 6.4vw;
        }
      }
      .billing-open-text {
        font-size: 4vw;
        color: #333333;
        font-weight: bold;
        margin-top: 6.8vw;
      }
    }
    .billing-status {
      width: 60%;
      height: 32.13vw;
      display: flex;
      flex-wrap: wrap;
      .billing-status-item {
        width: 33.33%;
        height: 50%;
        border-left: 0.13vw solid #e2e2e2;
        box-sizing: border-box;
        flex-direction: column;
        .status-icon {
          width: 6.8vw;
          height: 6.8vw;
          position: relative;
          image {
            width: 100%;
            height: 100%;
          }
          .status-item-num {
            width: 5.7vw;
            height: 5.7vw;
            background: #e60707;
            position: absolute;
            top: -2vw;
            right: -2vw;
            border-radius: 50%;
            font-size: 2.67vw;
            color: #fff;
            z-index: 2;
          }
        }
        .billing-status-item-text {
          font-size: 3.47vw;
          color: #333333;
          margin-top: 1.47vw;
          text-align: center;
        }
      }
    }
  }
  .menu-list {
    width: 100%;
    background: #fff;
    margin-top: 2.47vw;
    display: flex;
    flex-wrap: wrap;
    padding: 3.47vw;
    box-sizing: border-box;
    .menu-list-item {
      width: 25%;
      height: 23.4vw;
      flex-direction: column;
      .platform-list-item-icon {
        width: 7.2vw;
        height: 7.2vw;
        image {
          width: 100%;
          height: 100%;
        }
      }
      .menu-list-item-icon {
        width: 10vw;
        height: 10vw;
        border-radius: 50%;
        background: #f4691e;
        image {
          width: 4vw;
          height: 4vw;
        }
      }

      .menu-list-item-text {
        font-size: 3.47vw;
        color: #333333;
        margin-top: 3.2vw;
      }
    }
  }
  .page-footer {
    width: 100%;
    height: 7.2vw;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    font-size: 2.4vw;
    color: #929292;
    padding: 0 3.47vw;
  }
}
.cover-view {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .cover-image {
    width: 394rpx;
    height: 328rpx;
    margin: 0 auto;
  }
}
</style>
