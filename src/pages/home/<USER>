<template>
  <uni-nav-bar
    :border="false"
    title="新增套餐"
    color="#fff"
    left-icon="left"
    @clickLeft="handleClickLeft"
    backgroundColor="var(--primary-color)"
  ></uni-nav-bar>
  <scroll-view scroll-y class="scroll-view">
    <uni-forms
      label-width="20vw"
      class="popup-form"
      ref="baseForm"
      :modelValue="baseFormData"
    >
      <uni-forms-item label="套餐名称">
        <uni-easyinput
          v-model="baseFormData.title"
          :clearable="false"
          placeholder="请输入套餐名称"
        />
      </uni-forms-item>
      <uni-forms-item label="套餐类型">
        <uni-data-checkbox
          mode="button"
          selectedColor="var(--primary-color)"
          selectedTextColor="var(--primary-color)"
          v-model="baseFormData.thetype"
          :localdata="effectiveType"
        ></uni-data-checkbox>
      </uni-forms-item>
      <uni-forms-item label="套餐折扣" v-if="baseFormData.thetype === 3">
        <uni-easyinput
          v-model.number="baseFormData.discount"
          :clearable="false"
          placeholder="请输入套餐折扣"
        />
      </uni-forms-item>
      <uni-forms-item label="有效方式">
        <uni-data-checkbox
          mode="button"
          selectedColor="var(--primary-color)"
          selectedTextColor="var(--primary-color)"
          v-model="baseFormData.effective_type"
          :localdata="mealType"
        ></uni-data-checkbox>
      </uni-forms-item>
      <uni-forms-item label="有效时间">
        <uni-datetime-picker
          v-model="baseFormData.effective_begin_date"
          :clear-icon="false"
          type="daterange"
        />
      </uni-forms-item>

      <uni-forms-item label="单卡次数">
        <uni-easyinput
          type="number"
          v-model="baseFormData.num"
          :clearable="false"
          placeholder="请输入单卡次数(次)"
        />
      </uni-forms-item>
      <uni-forms-item label="服务内容">
        <uni-easyinput
          type="textarea"
          v-model="baseFormData.content"
          :clearable="false"
          placeholder="请输入服务内容"
        />
      </uni-forms-item>
      <uni-forms-item label="发行总量">
        <uni-easyinput
          v-model="baseFormData.postnum"
          type="number"
          :clearable="false"
          placeholder="请输入发行总量(张)"
        />
      </uni-forms-item>
      <uni-forms-item label="发行单价">
        <uni-easyinput
          v-model="baseFormData.price"
          type="number"
          :clearable="false"
          placeholder="请输入发行单价(元)"
        />
      </uni-forms-item>
      <view class="decs">
        说明 <br />
        1、服务类套餐创建后，请到库存管理中配置对应的套餐服务和套餐产品；<br />
        2、所有的套餐类型，如果创建了时间有效期，则会在有效期时间到期后自动失效；<br />
        3、套餐和折扣这两个类别，在开单时，套餐和折扣不能同时使用，例如：<br />
        A、车主（郭某某）到门店使用次卡（或者套餐卡）的同时，不能因为是会员，在套餐基础上再打折扣！<br />
        B、车主（郭某某）到门店使用次卡（或者套餐卡）的同时，有套餐以外的服务或产品需求，可以在套餐基础上选择护会员卡，用于结算套餐服务以外的产品和服务。
      </view>
      <view class="warehouse-add" @click="submit"> 确定 </view>
    </uni-forms>
  </scroll-view>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { addPackage } from "@/api/package";
import type { Response } from "@/utils/type";

const handleClickLeft = () => {
  uni.navigateBack();
};
const baseFormData = reactive({
  title: "", // 套餐名称
  effective_type: 1, // 有效方式  1固定时长 2一直有效(储值)  3开卡计时 （储值时前端默认传2）
  effective_begin_date: "", // 开始日期Y-m-d(固定时长不能为空)
  effective_end_date: "", // 截止日期Y-m-d(固定时长不能为空)
  effective_date_type: "", // 固定时常有效期类型 1:6月起，2:1年期，3:2年期(开卡计时不能为空)
  thetype: 1, // 套餐类型 1次卡 2套餐 3折扣 4储值
  num: undefined, // 单卡次数,不传或者为0表示不限制次数(次卡时必传)
  discount: undefined, // 折扣 按百分比 0~100(折扣时必传)
  content: "", // 服务内容
  postnum: undefined, // 发行数量 单位张
  price: undefined, // 价格
});
// 有效方式
const effectiveType = ref([
  {
    value: 1,
    text: "次卡",
  },
  {
    value: 2,
    text: "套餐",
  },
  {
    value: 3,
    text: "折扣",
  },
  {
    value: 4,
    text: "储值",
  },
]);
const mealType = ref([
  {
    value: 1,
    text: "固定时长",
  },
  {
    value: 3,
    text: "开卡计时",
  },
]);
// 表单验证
const validate = () => {
  if (baseFormData.thetype === 1) {
    if (!baseFormData.num) {
      uni.showToast({
        title: "请输入单卡次数",
        icon: "none",
      });
      return false;
    }
  }
  if (!baseFormData.title) {
    uni.showToast({
      title: "请输入套餐名称",
      icon: "none",
    });
    return false;
  }
  if (baseFormData.effective_type === 1) {
    if (!baseFormData.effective_begin_date) {
      uni.showToast({
        title: "请输入有效时间",
        icon: "none",
      });
      return false;
    }
  }
  if (baseFormData.thetype === 3) {
    if (!baseFormData.discount) {
      uni.showToast({
        title: "请输入套餐折扣",
        icon: "none",
      });
      return false;
    }
  }
  return true;
};
// 提交
const submit = async () => {
  if (!validate()) return;
  let params = baseFormData;
  params.title = baseFormData.title;
  params.effective_type = baseFormData.effective_type === 1 ? 1 : 2;
  params.effective_begin_date = baseFormData.effective_begin_date[0];
  params.effective_end_date = baseFormData.effective_begin_date[1];
  params.effective_date_type = baseFormData.effective_date_type;
  params.thetype = baseFormData.thetype;
  params.num = baseFormData.num;
  params.discount = baseFormData.discount;
  params.content = baseFormData.content;
  params.postnum = baseFormData.postnum;
  params.price = baseFormData.price;
  
  let res: Response = (await addPackage(params)) as Response;
  if (res.code === 200) {
    uni.showToast({
      title: "新增成功",
      icon: "success",
    });
    uni.navigateBack();
  } else {
    uni.showToast({
      title: res.message,
      icon: "none",
    });
  }
};
</script>

<style scoped lang="scss">
.scroll-view {
  padding-bottom: 16.33vw;
}
.popup-form {
  padding: 3.5vw;

  .uni-forms-item {
    margin-bottom: 3.5vw;
  }

  .vip-icon {
    position: absolute;
    width: 5.07vw;
    height: 2.67vw;
    left: -8vw;
    top: 1.8vw;
  }
  .warehouse-add {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 15.33vw;
    background: var(--primary-color);
    border-radius: 1.33vw 1.33vw 0vw 0vw;
    text-align: center;
    line-height: 15.33vw;
    color: #fff;
    font-size: 4.8vw;
  }
  .decs {
    font-size: 3.73vw;
    color: #1d1d1d;
    line-height: 5.8vw;
  }
}
:deep(.uni-data-checklist .checklist-group .checklist-box.is--button) {
  padding: 8px 10px;
  margin: 0 10px 0 0;
}
</style>
