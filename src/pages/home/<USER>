<template>
  <view class="platform-service-page">
    <!-- 顶部导航 -->
    <uni-nav-bar
      title="平台服务"
      left-icon="left"
      @clickLeft="onBack"
      background-color="#1989fa"
      color="#fff"
      status-bar
    />

    <!-- 套餐Tab -->
    <view class="tabs">
      <view
        v-for="(tab, idx) in tabList"
        :key="tab.value"
        :class="['tab', { active: currentTab === idx }]"
        @click="currentTab = idx"
        :style="{ color: tab.color }"
      >{{ tab.label }}</view>
    </view>

    <!-- 套餐卡片 -->
    <view class="service-card" :style="{ background: tabList[currentTab].bg }">
      <view class="price-row">
        <text class="price">￥{{ tabList[currentTab].price }}</text>
        <text class="unit">/年</text>
      </view>
      <view class="desc">{{ tabList[currentTab].desc }}</view>
      <view class="target">{{ tabList[currentTab].target }}</view>
    </view>

    <!-- 功能对比表 -->
    <scroll-view scroll-x class="table-scroll">
      <view class="table">
        <view class="table-header">
          <view class="cell name-cell">功能名称</view>
          <view
            v-for="col in columns"
            :key="col.key"
            class="cell"
            :style="{ color: col.color }"
          >{{ col.label }}</view>
        </view>
        <view
          v-for="row in tableData"
          :key="row.name"
          class="table-row"
        >
          <view class="cell name-cell">{{ row.name }}</view>
          <view
            v-for="col in columns"
            :key="col.key"
            class="cell"
          >
            <uni-icons
              :type="row[col.key] ? 'checkbox-filled' : 'closeempty'"
              :color="row[col.key] ? '#4cd964' : '#ff4d4f'"
              size="22"
            />
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 底部按钮 -->
    <view class="footer-btn">
      <button class="buy-btn">确定购买</button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

const tabList = [
  {
    label: '基础版',
    value: 'basic',
    price: 688,
    desc: '适用于单门店使用',
    target: '车牌专车号牌一键识别\n车主专属档案管理',
    color: '#1989fa',
    bg: 'linear-gradient(90deg, #1989fa 0%, #4fc3f7 100%)'
  },
  {
    label: '标准版',
    value: 'standard',
    price: 1288,
    desc: '团队协作/共享更智能\n更多门店功能开放',
    target: '适用于多门店管理',
    color: '#ff9800',
    bg: 'linear-gradient(90deg, #ff9800 0%, #ffc107 100%)'
  },
  {
    label: '专业版',
    value: 'pro',
    price: 3186,
    desc: '适用于大型门店\n自建品牌/私域数据',
    target: '支持更多门店和功能扩展',
    color: '#ff9800',
    bg: 'linear-gradient(90deg, #ff9800 0%, #ffc107 100%)'
  }
]
const currentTab = ref(0)

const columns = [
  { key: 'free', label: '免费版', color: '#43cf7c' },
  { key: 'basic', label: '基础版', color: '#1989fa' },
  { key: 'standard', label: '标准版', color: '#ff9800' },
  { key: 'pro', label: '专业版', color: '#ff9800' }
]

// 示例数据，实际可从接口获取
const tableData = [
  { name: '接待维修开单', free: true, basic: true, standard: true, pro: true },
  { name: '客户档案管理', free: false, basic: true, standard: true, pro: true },
  { name: '车主信息识别', free: false, basic: true, standard: true, pro: true },
  { name: '车主一键识别', free: false, basic: true, standard: true, pro: true },
  { name: 'VIN一键识别', free: false, basic: true, standard: true, pro: true },
  { name: '基础库存管理', free: false, basic: true, standard: true, pro: true },
  { name: '电子保养手册', free: false, basic: false, standard: true, pro: true },
  { name: '财务管理统计', free: false, basic: true, standard: true, pro: true },
  { name: '记账管理系统', free: false, basic: false, standard: true, pro: true },
  // ...更多功能
]
const onBack = () => {
  uni.navigateBack()
}
</script>

<style scoped lang="scss">
.platform-service-page {
  min-height: 100vh;
  background: #f5f5f5;
  .tabs {
    display: flex;
    justify-content: space-around;
    background: #fff;
    .tab {
      flex: 1;
      text-align: center;
      font-size: 30rpx;
      padding: 28rpx 0;
      font-weight: bold;
      border-bottom: 4rpx solid transparent;
      &.active {
        border-bottom: 4rpx solid #1989fa;
      }
    }
  }
  .service-card {
    margin: 24rpx;
    border-radius: 18rpx;
    padding: 32rpx 28rpx;
    color: #fff;
    .price-row {
      display: flex;
      align-items: flex-end;
      .price {
        font-size: 48rpx;
        font-weight: bold;
      }
      .unit {
        font-size: 28rpx;
        margin-left: 8rpx;
      }
    }
    .desc {
      margin: 12rpx 0 8rpx 0;
      font-size: 28rpx;
    }
    .target {
      font-size: 24rpx;
      opacity: 0.9;
      white-space: pre-line;
    }
  }
  .table-scroll {
    margin: 0 0 120rpx 0;
    background: #fff;
    border-radius: 18rpx;
    overflow-x: auto;
  }
  .table {
    min-width: 800rpx;
    .table-header, .table-row {
      display: flex;
      align-items: center;
      border-bottom: 1px solid #f0f0f0;
    }
    .table-header {
      background: #f7f7f7;
      font-weight: bold;
      .cell {
        padding: 18rpx 12rpx;
        min-width: 120rpx;
        text-align: center;
      }
      .name-cell {
        min-width: 180rpx;
        text-align: left;
      }
    }
    .table-row {
      .cell {
        padding: 18rpx 12rpx;
        min-width: 120rpx;
        text-align: center;
      }
      .name-cell {
        min-width: 180rpx;
        text-align: left;
      }
    }
  }
  .footer-btn {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    padding: 24rpx 32rpx;
    box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.04);
    .buy-btn {
      width: 100%;
      height: 88rpx;
      background: #1989fa;
      color: #fff;
      font-size: 32rpx;
      border-radius: 12rpx;
      font-weight: bold;
    }
  }
}
</style>

