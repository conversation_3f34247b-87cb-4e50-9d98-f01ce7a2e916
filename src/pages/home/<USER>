<template>
  <uni-nav-bar
    :border="false"
    title="服务管理"
    color="#fff"
    left-icon="left"
    right-text="新增"
    @clickRight="handleClickRight"
    @clickLeft="handleClickLeft"
    backgroundColor="var(--primary-color)"
  ></uni-nav-bar>
  <scroll-view class="service-manage" scroll-y>
    <view class="service-top">
      <uni-easyinput
        class="search-input"
        suffixIcon="search"
        v-model="searchValue"
        placeholder="请输入服务名称查询"
        @iconClick="handleSearch"
      ></uni-easyinput>
    </view>
    <view class="tips-text">向左滑动可以删除服务</view>
    <uni-swipe-action>
      <uni-swipe-action-item
        style="margin-top: 3.2vw"
        v-for="item in serviceList"
        :key="item.id"
      >
        <view class="service-list-item" @tap="handleClickDetail(item)">
          <view class="item-row flex-sbw">
            <view class="service-item-name">{{ item.name }}</view>
            <view class="service-type">{{ getServiceType(item.type) }}</view>
          </view>
          <view class="item-row flex-sbw">
            <view>价格：<text class="color1">{{ item.price }}</text>元</view>
            <view>时长：<text class="color4">{{ item.duration }}</text>分钟</view>
          </view>
          <view class="row-content">描述：{{ item.description }}</view>
          <view class="item-row flex-sbw">
            <view>创建时间：<text class="color3">{{ item.create_time }}</text></view>
          </view>
        </view>
        <template #right>
          <view class="right-item flex-center" @click.native="handleDelete(item)">
            <image src="@/static/images/warehouse/delete.png" mode="aspectFit" />
            删<br />除
          </view>
        </template>
      </uni-swipe-action-item>
    </uni-swipe-action>
  </scroll-view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getAllServiceList, deleteService } from '@/api/service';
import type { TableData } from '@/utils/type';

interface ServiceItem {
  id: number;
  name: string;
  price: number;
  type: 1 | 2 | 3; // 1: 基础服务, 2: 增值服务, 3: 特殊服务
  duration: number;
  description: string;
  create_time: string;
}

interface ApiResponse<T> {
  code: number;
  data: T;
  message?: string;
}

interface ServiceListResponse {
  list: ServiceItem[];
  total: number;
}

const searchValue = ref('');
const serviceList = ref<ServiceItem[]>([]);

// 获取服务类型文本
const getServiceType = (type: 1 | 2 | 3) => {
  const typeMap: Record<1 | 2 | 3, string> = {
    1: '基础服务',
    2: '增值服务',
    3: '特殊服务'
  };
  return typeMap[type] || '未知类型';
};

// 获取服务列表
const getList = async () => {
  try {
    const params: TableData = {
      page: 1,
      limit: 10,
      name: searchValue.value
    };
    const res = (await getAllServiceList(params)) as ApiResponse<ServiceListResponse>;
    if (res.code === 200) {
      serviceList.value = res.data.list;
    }
  } catch (error) {
    console.error('获取服务列表失败:', error);
    uni.showToast({
      title: '获取服务列表失败',
      icon: 'none'
    });
  }
};

// 搜索
const handleSearch = () => {
  getList();
};

// 删除服务
const handleDelete = async (item: ServiceItem) => {
  try {
    const res = await deleteService({ id: item.id });
    if (res.code === 200) {
      uni.showToast({
        title: '删除成功',
        icon: 'success'
      });
      getList();
    }
  } catch (error) {
    console.error('删除服务失败:', error);
    uni.showToast({
      title: '删除失败',
      icon: 'none'
    });
  }
};

// 查看详情
const handleClickDetail = (item: ServiceItem) => {
  uni.navigateTo({
    url: `/pages/home/<USER>
  });
};

// 新增服务
const handleClickRight = () => {
  uni.navigateTo({
    url: '/pages/home/<USER>'
  });
};

// 返回
const handleClickLeft = () => {
  uni.navigateBack();
};

onMounted(() => {
  getList();
});
</script>

<style scoped lang="scss">
.color1 {
  color: var(--warning-color);
}
.color3 {
  color: var(--danger-color);
}
.color4 {
  color: var(--primary-color);
}

.service-manage {
  height: calc(100% - 44px);
  box-sizing: border-box;
  background-color: #f2f2f2;
  
  .service-top {
    padding: 3.5vw;
    background-color: #fff;
    
    .search-input {
      margin-bottom: 3.5vw;
    }
  }
  
  .tips-text {
    padding-top: 3.2vw;
    text-align: center;
    font-size: 3.47vw;
    color: #8f8e8e;
  }
  
  .service-list-item {
    font-size: 3.47vw;
    color: #8f8e8e;
    padding: 1.5vw;
    background-color: #fff;
    width: 92.8vw;
    margin: 0 auto;
    border-radius: 0.67vw;
    
    .item-row {
      height: 10vw;
      
      .service-item-name {
        color: #fff;
        font-size: 3.47vw;
        border-radius: 0.67vw;
        width: 45vw;
        height: 8.8vw;
        padding: 0 2.4vw;
        box-sizing: border-box;
        line-height: 8.8vw;
        text-align: center;
        background-color: var(--primary-color);
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      
      .service-type {
        font-size: 3.47vw;
        color: var(--primary-color);
      }
    }
    
    .row-content {
      width: 100%;
      height: 10vw;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      display: flex;
      align-items: center;
      flex-shrink: 0;
    }
  }
  
  .right-item {
    width: 19vw;
    height: 100%;
    background-color: var(--danger-color);
    color: #fff;
    font-size: 3.47vw;
    flex-direction: column;
    border-radius: 1.33vw;
    
    image {
      width: 4.53vw;
      height: 4.53vw;
      margin-bottom: 1.33vw;
    }
  }
}
</style> 