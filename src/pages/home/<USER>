<template>
  <uni-nav-bar
    :border="false"
    title="会员管理"
    color="#fff"
    left-icon="left"
    right-text="开卡"
    @clickRight="handleClickRight"
    @clickLeft="handleClickLeft"
    backgroundColor="var(--primary-color)"
  ></uni-nav-bar>
  <scroll-view class="vip-manage" scroll-y>
    <view class="vip-top">
      <uni-easyinput
        class="search-input"
        suffixIcon="search"
        v-model="value"
        placeholder="请输入车牌、客户姓名、手机号码查询"
        @iconClick="iconClick"
      ></uni-easyinput>
    </view>
    <view class="vip-list">
      <view class="vip-manage-item">
        <view class="vip-row flex-sbw">
          <view class="vip-name">张贸然</view>
          <view class="vip-right flex-sbw">
            <view class="vip-phone">138****8000
              <image class="vip-icon" src="@/static/images/warehouse/vip_blue.png" mode="aspectFit" />
            </view>
            <view class="vip-btn flex-center">开单</view>
          </view>
        </view>
        <view class="vip-row flex-sbw">
          <view class="vip-btn">粤CF521D</view>
          <view class="vip-btn">核销</view>
          <view class="vip-btn">开卡</view>
          <view class="vip-btn">详情</view>
        </view>
      </view>
    </view>
  </scroll-view>
</template>

<script setup lang="ts">
import { ref } from "vue";
// import { getVipList } from "@/api/vip";
const value = ref("");
const iconClick = () => {
  console.log("iconClick");
};
const handleClickLeft = () => {
  uni.navigateBack();
};

const handleClickRight = () => {
  /* uni.navigateTo({
    url: "/pages/home/<USER>/addVip",
  }); */
};
</script>

<style scoped lang="scss">
.vip-manage {
  height: calc(100% - 44px);
  box-sizing: border-box;
  background-color: #f2f2f2;
  .vip-top {
    padding: 3.5vw;
  }
  .vip-list {
    padding-bottom: 10vw;
    .vip-manage-item {
      padding: 0 3.5vw;
      background-color: #fff;
      border-radius: 0.67vw;
      color: #333333;
      .vip-row {
        height: 11.6vw;
        .vip-name {
          font-size: 3.47vw;
          color: var(--primary-color);
        }
        .vip-right {
          width: 45vw;
          .vip-btn {
            width: 11.8vw;
            height: 9.3vw;
            border-radius: 1.33vw;
            background-color: var(--primary-color);
            color: #fff;
            font-size: 3.73vw;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 1.33vw;
            border: none;
            margin-right: 0;
            padding: 0;
          }
          .vip-phone {
            position: relative;
            .vip-icon {
              width: 4.93vw;
              height: 3.6vw;
              position: absolute;
              right: -6.6vw;
              top: -0.93vw;
            }
          }
        }
        .vip-btn {
          border-radius: 1.33vw;
          border: 1px solid #333333;
          color: #333333;
          padding: 1.33vw 3.2vw;
          &:first-child {
            margin-right: 10vw;
          }
        }
      }
    }
  }
}
</style>
