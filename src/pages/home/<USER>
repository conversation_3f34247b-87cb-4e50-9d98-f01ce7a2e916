<template>
  <scroll-view scroll-y class="order-scroll">
    <uni-nav-bar
      :border="false"
      title="资质认证"
      color="#fff"
      left-icon="left"
      @clickLeft="handleClickLeft"
      backgroundColor="var(--primary-color)"
      fixed
      status-bar
    ></uni-nav-bar>
    <view class="order">
      <uni-forms ref="valiForm" :modelValue="valiFormData">
        <uni-forms-item label="企业名称：" label-width="22.4vw" name="name">
          <input
            type="text"
            v-model="valiFormData.store_name"
            placeholder="请输入企业名称"
          />
        </uni-forms-item>
        <uni-forms-item label="所在城市：" label-width="22.4vw" name="address">
          <l-cascader v-model="address" />
        </uni-forms-item>
        <uni-forms-item
          label="详细地址："
          label-width="22.4vw"
          name="addressDetail"
        >
          <input
            type="text"
            v-model="valiFormData.address"
            placeholder="请输入详细地址"
          />
          <!-- <uni-icons
          @click="handleLocation"
          class="location-icon"
          type="location"
          size="16"
          color="#086bdb"
        /> -->
        </uni-forms-item>
        <uni-forms-item label="联系电话：" label-width="22.4vw" name="phone">
          <input
            type="text"
            v-model="valiFormData.phone"
            placeholder="请输入联系电话"
          />
        </uni-forms-item>
        <uni-forms-item label="联系人：" label-width="22.4vw" name="contact">
          <input
            type="text"
            v-model="valiFormData.contact"
            placeholder="请输入联系人"
          />
        </uni-forms-item>
      </uni-forms>
      <view class="image-upload">
        <view class="image-upload-item">
          <view
            class="image-upload-item-box"
            @click="handleUpload('pic_business')"
          >
          </view>
          <view class="image-text">上传营业执照</view>
        </view>
        <view class="image-upload-item image-upload-example">
          <view class="example-left">
            <view class="image-upload-item-box">
              <image src="@/static/images/other/license_example.png" />
            </view>
            <view class="image-text">正确示范</view>
          </view>
          <view class="example-right"
            >请保持营业执照正 面上传，上传后系 统将自动加上水印
            “仅限认证复印无效”</view
          >
        </view>
        <view
          class="image-upload-item"
          v-for="item in uploadList"
          :key="item.key"
        >
          <view class="image-upload-item-box" @click="handleUpload(item.key)">
          </view>
          <view class="image-text">{{ item.name }}</view>
        </view>
      </view>
      <view class="agree-checkbox">
        <checkbox-group @change="handleAgreeChange">
          <label>
            <checkbox value="cb" :checked="agreeChecked" />
          </label>
        </checkbox-group>

        <text>同意</text>
        <text class="link">《车店助手用户服务协议》</text>
        <text class="link">《系统配件产品采购服务协议》</text>
      </view>

      <uv-picker
        ref="picker"
        :columns="shareholderList"
        keyName="fullname"
        @confirm="handlePickerConfirm"
      ></uv-picker>
    </view>
    <view class="button-view">
      <view class="transfer-button" @click="handleTransfer">转&emsp;让</view>
      <view class="submit-button" @click="handleSubmit">提&emsp;交</view>
    </view>
  </scroll-view>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { uploadFile } from "@/utils/file";
import {
  certification,
  getStoreInfo,
  transfer,
  getShareholderList,
} from "@/api/store";
import type { Response, TableData } from "@/utils/type";
import lCascader from "@/components/lCascader/lCascader.vue";
const handleUpload = async (type: string) => {
  if (!agreeChecked.value) {
    uni.showToast({
      title: "请先同意协议",
      icon: "none",
    });
    return;
  }
  const res: Response = JSON.parse((await uploadFile(1)) as string);
  if (res.code === 200) {
    valiFormData.value[type] = res.data.paths[0];
  }
};
const picker = ref();
const address = ref([]);
const handleAgreeChange = () => {
  agreeChecked.value = !agreeChecked.value;
};
const uploadList = ref([
  {
    name: "身份证人像面",
    key: "pic_idcard1",
  },
  {
    name: "身份证国徽面",
    key: "pic_idcard2",
  },
  {
    name: "资质证书",
    key: "pic_qualifications",
  },
  {
    name: "店厂招牌",
    key: "pic_sign",
  },

  {
    name: "环境照片",
    key: "pic_environment",
  },
]);
const valiFormData = ref<TableData>({
  store_name: "", // 门店名称
  province_id: 0, // 省份ID
  city_id: 0, // 城市ID
  districtcounty_id: 0, // 区县ID
  address: "", // 门店地址
  phone: "", // 联系电话
  contact: "", // 联系人
  pic_business: "", // 营业执照图片
  pic_qualifications: "", // 资质证书图片
  pic_idcard1: "", // 身份证正面图
  pic_idcard2: "", // 身份证反面图
  pic_sign: "", // 店厂招牌图片
  pic_environment: "", // 环境照片
});
const agreeChecked = ref(false);
const handleClickLeft = () => {
  uni.navigateBack();
};
const handleSubmit = async () => {
  if (!agreeChecked.value) {
    uni.showToast({
      title: "请先同意协议",
      icon: "none",
    });
    return;
  }
  if (
    !valiFormData.value.store_name ||
    !valiFormData.value.phone ||
    !valiFormData.value.contact ||
    !valiFormData.value.pic_business ||
    !valiFormData.value.pic_qualifications ||
    !valiFormData.value.pic_idcard1 ||
    !valiFormData.value.pic_idcard2
  ) {
    uni.showToast({
      title: "必填字段不能为空",
      icon: "none",
    });
    return;
  }
  const res: Response = (await certification(valiFormData.value)) as Response;
  if (res.code === 200) {
    uni.switchTab({
      url: "/pages/home/<USER>",
    });
  }
};
// 获取门店信息
const storeInfo = ref({});
const getStoreInfoData = async () => {
  try {
    const res: Response = (await getStoreInfo({})) as Response;
    if (res.code === 200) {
      storeInfo.value = res.data;
      getShareholderListData();
    }
  } catch (error) {}
};
const handleTransfer = () => {
  picker.value.open();
};
// 获取股东列表
const shareholderList = ref<any[]>([]);
const getShareholderListData = async () => {
  const res: Response = (await getShareholderList({})) as Response;
  if (res.code === 200) {
    shareholderList.value = [res.data];
  }
};
const handlePickerConfirm = async (e: any) => {
  const { indexs } = e;
  const res: Response = (await transfer({
    user_id: shareholderList.value[indexs[0]].id,
  })) as Response;
  if (res.code === 200) {
    uni.showToast({
      title: "转让成功",
      icon: "success",
    });
    uni.switchTab({
      url: "/pages/home/<USER>",
    });
  }
};
watch(address, (newVal: any) => {
  valiFormData.value.province_id = newVal[0];
  valiFormData.value.city_id = newVal[1];
  valiFormData.value.districtcounty_id = newVal[2];
  valiFormData.value.address = newVal[3] || "";
});
onMounted(() => {
  getStoreInfoData();
});
</script>

<style scoped lang="scss">
.order-scroll {
  height: 100vh;
}
.order {
  padding: 4.27vw;

  :deep(.uni-forms-item) {
    border-bottom: 1upx solid #f6f6f6;
    margin-bottom: 0;
    height: 10.8vw;
    .uni-forms-item__label {
      font-size: 3.2vw;
      color: #1a1a1a;
    }
    .uni-forms-item__content {
      display: flex;
      align-items: center;
      input {
        flex: 1;
        font-size: 3.2vw;
        color: #1a1a1a;
      }
      .location-icon {
        margin-left: 2.67vw;
      }
    }
  }

  .image-upload {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding-top: 5.33vw;
    .image-upload-item {
      width: 41.7vw;
      &:nth-child(5),
      &:nth-child(6),
      &:nth-child(7) {
        width: 28.53vw;
      }
      &.image-upload-example {
        display: flex;
        justify-content: space-between;
        width: 45vw;
        height: 100%;
        .example-left {
          width: 19.2vw;
          .image-upload-item-box {
            border: none;
          }
        }
        .example-right {
          width: 22vw;
          font-size: 2.67vw;
          color: #1a1a1a;
          line-height: 6.5vw;
        }
      }
      .image-upload-item-box {
        width: 100%;
        height: 28.53vw;
        border-radius: 10upx;
        border: 1upx solid #d4d4d4;
        background-color: #f6f6f6;
        background: url("@/static/images/other/img_select.png") no-repeat center
          center;
        background-size: 8.93vw 7.33vw;
        image {
          width: 100%;
          height: 100%;
        }
      }
      .image-text {
        font-size: 3.2vw;
        color: #1a1a1a;
        text-align: center;
        padding: 3.2vw 0 5.2vw;
      }
    }
  }
  .agree-checkbox {
    display: flex;
    align-items: center;
    padding: 6vw 0;
    font-size: 2.67vw;
    color: #1a1a1a;
    .link {
      color: #086bdb;
      font-size: 2.67vw;
    }
    :deep(.uni-checkbox-input) {
      width: 3.2vw;
      height: 3.2vw;
    }
  }
}
.button-view {
  display: flex;
  .submit-button {
    height: 14.4vw;
    background-color: #086bdb;
    color: #fff;
    font-size: 4.53vw;
    text-align: center;
    line-height: 14.4vw;
    flex: 1;
  }
  .transfer-button {
    width: 25.2vw;
    background-color: #a09f9d;
    color: #fff;
    font-size: 4.53vw;
    text-align: center;
    line-height: 14.4vw;
    flex-shrink: 0;
  }
}
</style>
