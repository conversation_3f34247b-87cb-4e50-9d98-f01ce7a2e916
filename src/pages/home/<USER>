<template>
  <view style="height: 100%">
    <uni-nav-bar
      :border="false"
      title="套餐详情"
      color="#fff"
      left-icon="left"
      @clickLeft="handleClickLeft"
      backgroundColor="var(--primary-color)"
    ></uni-nav-bar>
    <scroll-view scroll-y class="meal-detail">
      <view class="meal-row-list">
        <view class="meal-row-item" v-for="item in rowList" :key="item.key">
          <text>{{ item.title }}</text>
          <text class="meal-row-item-content">{{
            baseData[item.key] || "--"
          }}</text>
        </view>
      </view>
      <view class="meal-list-item">
        <view class="item-row flex-sbw">
          <view
            >客户名称：<text class="color4">{{
              baseData.customer_info.fullname
            }}</text></view
          >
          <view>{{ baseData.customer_info.phone }}</view>
        </view>
        <view class="item-row flex-sbw">
          <view
            >套餐次数：<text class="color4">{{ baseData.all_num }}</text
            >次</view
          >
          <view
            >已用次数：<text class="color4">{{ baseData.used_num }}</text
            >次</view
          >
          <view
            >剩余次数：<text class="color4">{{ baseData.surplus_num }}</text
            >次</view
          >
        </view>
      </view>
      <view class="table-box">
        <view class="table-head">
          <view class="table-cell">使用时间</view>
          <view class="table-cell">订单编号</view>
          <view class="table-cell">操作</view>
        </view>
        <view class="table-body">
          <view class="table-row" v-for="item in recordData" :key="item.id">
            <view class="table-cell">{{
              item.create_time ? item.create_time : "--"
            }}</view>
            <view class="table-cell">{{ item.order_info.ordercode }}</view>
            <view class="table-cell">
              <text>详情</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { onLoad } from "@dcloudio/uni-app";
import { ref } from "vue";
import { getPackageDetail, getPackageUsageRecord } from "@/api/package";
import type { Response, TableData } from "@/utils/type";

onLoad((options) => {
  if (options && options.id) {
    getDetail(Number(options.id));
  }
});
const rowList = ref([
  {
    title: "套餐编码：",
    content: "",
    key: "package_code",
  },
  {
    title: "套餐名称：",
    content: "",
    key: "title",
  },
  {
    title: "开通时间：",
    content: "",
    key: "effective_begin_date",
  },
  {
    title: "到期时间：",
    content: "",
    key: "effective_end_date",
  },
  {
    title: "套餐类型：",
    content: "",
    key: "thetype",
  },
  {
    title: "作废时间：",
    content: "",
    key: "tovoid_time",
  },
  {
    title: "服务内容：",
    content: "",
    key: "content",
  },
]);
const baseData = ref<TableData>({});
const recordData = ref<TableData[]>([]);
// 套餐使用记录
const getRecordData = async () => {
  const res: Response = (await getPackageUsageRecord({
    package_id: baseData.value.id,
    car_id: 0,
  })) as Response;
  if (res.code === 200) {
    recordData.value = res.data.list;
  }
};
const getDetail = async (id: number) => {
  const res = (await getPackageDetail({
    id,
  })) as Response;
  if (res.code === 200) {
    baseData.value = res.data;
    getRecordData();
  }
};

const handleClickLeft = () => {
  uni.navigateBack();
};
</script>

<style scoped lang="scss">
.color4 {
  color: var(--primary-color);
}
.meal-detail {
  background-color: #f5f5f5;
  height: calc(100% - 44px);
  .meal-row-list {
    padding: 3.5vw;
    background-color: #fff;
    .meal-row-item {
      font-size: 3.73vw;
      color: #8f8e8e;
      padding-bottom: 3.2vw;
      line-height: 5.8vw;
      .meal-row-item-content {
        color: var(--primary-color);
        word-break: break-all;
        overflow-wrap: break-word;
      }
    }
  }
  .meal-list-item {
    font-size: 3.47vw;
    color: #8f8e8e;
    padding: 2.4vw;
    background-color: #fff;
    width: 92.8vw;
    margin: 2.8vw auto 0;
    border-radius: 0.67vw;
    box-sizing: border-box;
    .item-row {
      height: 10vw;
      &:first-child {
        font-size: 4.8vw;
      }
      .user-item-name {
        width: 32vw;
        height: 8.8vw;
        padding: 0 2.4vw;
        line-height: 8.8vw;
        text-align: center;
        box-sizing: border-box;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        font-size: 3.47vw;
        color: var(--primary-color);
      }
    }
    .row-content {
      width: 100%;
      height: 10vw;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      display: flex;
      align-items: center;
      flex-shrink: 0;
    }
  }
  .table-box {
    width: 92.8vw;
    margin: 0 auto;
    box-sizing: border-box;
    .table-head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 13.5vw;
      color: #333333;
      font-size: 4.2vw;
      .table-cell {
        &:last-child {
          color: #333333;
        }
      }
    }
    .table-body {
      background-color: #fff;
      border-radius: 0.67vw;

      .table-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 13.5vw;
        font-size: 3.47vw;
      }
    }
    .table-cell {
      width: 60%;
      text-align: center;
      &:last-child {
        width: 15%;
        flex-shrink: 0;
        color: var(--primary-color);
      }
    }
  }
}
</style>
