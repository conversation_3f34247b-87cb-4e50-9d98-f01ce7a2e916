<template>
  <view class="statistics-page">
    <!-- 顶部导航栏 -->
    <uni-nav-bar
      :border="false"
      title="统计分析"
      color="#fff"
      left-icon="left"
      backgroundColor="#1976d2"
      @clickLeft="handleBack"
    />

    <!-- 时间筛选 -->
    <view class="date-tabs">
      <view :class="['tab', {active: dateType==='月'}]" @click="dateType='月'">月</view>
      <view :class="['tab', {active: dateType==='季'}]" @click="dateType='季'">季</view>
      <view :class="['tab', {active: dateType==='年'}]" @click="dateType='年'">年</view>
    </view>

    <!-- 折线图 -->
    <view class="chart-section">
      <ec-canvas id="lineChart" ref="lineChart" :option="lineOption" canvas-id="lineChart" style="width:100%;height:220px" />
    </view>

    <!-- 饼图和统计卡片 -->
    <view class="pie-section">
      <view class="pie-left">
        <ec-canvas id="pieChart" ref="pieChart" :option="pieOption" canvas-id="pieChart" style="width:140px;height:140px" />
      </view>
      <view class="pie-right">
        <view class="pie-row" v-for="(item, idx) in pieList" :key="idx">
          <text class="pie-label">{{item.label}}：</text>
          <text class="pie-value">{{item.value}}</text>
          <text class="pie-rate">占比: {{item.rate}}</text>
        </view>
      </view>
    </view>

    <!-- 会员/储值卡统计 -->
    <view class="card-section">
      <view class="card-item">
        <text class="card-num">321</text>
        <text class="card-label">总会员数</text>
      </view>
      <view class="card-item">
        <text class="card-num green">4</text>
        <text class="card-label">新增长会员</text>
      </view>
      <view class="card-item">
        <text class="card-num blue">240</text>
        <text class="card-label">储值金额</text>
      </view>
    </view>

    <!-- 收入支出列表 -->
    <view class="income-section">
      <view class="income-bar">
        <view class="income-tab active">本周收入：<text class="income-num">37289.00元</text></view>
        <view class="income-tab">本周支出：<text class="income-num">37289.00元</text></view>
      </view>
      <view class="income-list">
        <view class="income-row" v-for="(item, idx) in incomeList" :key="idx">
          <text class="income-date">{{item.date}}</text>
          <text class="income-type">{{item.type}}</text>
          <text class="income-car">{{item.car}}</text>
          <text class="income-amount">{{item.amount}}</text>
          <text class="income-action">查看</text>
        </view>
      </view>
    </view>

    <!-- 商品排行表格 -->
    <view class="table-section">
      <view class="table-header">
        <text class="th">排行</text>
        <text class="th">产品名称</text>
        <text class="th">库存销量</text>
        <text class="th">采购单价</text>
        <text class="th">环比</text>
        <text class="th">走势</text>
      </view>
      <view class="table-row" v-for="(item, idx) in productList" :key="idx">
        <text class="td rank">{{idx+1}}</text>
        <text class="td name">{{item.name}}</text>
        <text class="td stock">{{item.stock}}</text>
        <text class="td price">{{item.price}}</text>
        <text class="td compare" :class="item.compare>0?'up':'down'">{{item.compare>0?'+':''}}{{item.compare}}</text>
        <text class="td trend">
          <text v-if="item.compare>0" class="icon up">↑</text>
          <text v-else class="icon down">↓</text>
        </text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
// 引入echarts组件（uni-app版）
// 需先安装：npm install @qiun/uni-echarts
// 并在pages.json注册easycom
// import EcCanvas from '@qiun/uni-echarts';

const dateType = ref('月');

const lineOption = ref({
  color: ['#1976d2', '#ff9900', '#ff3b30'],
  legend: {
    data: ['成本', '支出', '毛利'],
    top: 10
  },
  grid: { left: 30, right: 20, top: 50, bottom: 30 },
  xAxis: {
    type: 'category',
    data: ['前期4/01', '前期4/08', '前期4/15', '前期4/22', '前期4/29']
  },
  yAxis: {
    type: 'value',
    min: 0,
    max: 50000
  },
  series: [
    {
      name: '成本',
      type: 'line',
      data: [12000, 18000, 15000, 20000, 30876],
      smooth: true
    },
    {
      name: '支出',
      type: 'line',
      data: [8000, 12000, 11000, 15000, 30827],
      smooth: true
    },
    {
      name: '毛利',
      type: 'line',
      data: [4000, 6000, 4000, 5000, 30876],
      smooth: true
    }
  ]
});

const pieOption = ref({
  color: ['#1976d2', '#ff9900', '#ff3b30'],
  series: [{
    type: 'pie',
    radius: ['60%', '80%'],
    center: ['50%', '50%'],
    label: { show: false },
    data: [
      { value: 230836, name: '客户价值' },
      { value: 230836, name: '新客户价值' },
      { value: 230836, name: '其他价值' }
    ]
  }]
});

const pieList = [
  { label: '客户价值', value: 230836, rate: '53.50%' },
  { label: '新客户价值', value: 230836, rate: '13.50%' },
  { label: '其他价值', value: 230836, rate: '13.50%' }
];

const incomeList = [
  { date: '25.04.14', type: '快修保养', car: '粤A68H6S', amount: 36721 },
  { date: '25.04.14', type: '洗车美容', car: '粤A68H6S', amount: 6721 },
  { date: '25.04.14', type: '换件维修', car: '粤A68H6S', amount: 721 },
  { date: '25.04.14', type: '记收入', car: '张敏', amount: 21 }
];

const productList = [
  { name: '速霸100（1L）', stock: 1350, price: 45, compare: 12 },
  { name: '速霸2000（1L）', stock: 250, price: 48, compare: -5 },
  { name: '嘉实多极护0W20', stock: 45, price: 43, compare: 3 },
  { name: '耐力多J377-LO7H0W20', stock: 25, price: 43, compare: -8 },
  { name: '嘉实多极护0W20', stock: 25, price: 43, compare: 3 }
];

function handleBack() {
  uni.navigateBack();
}
</script>

<style scoped lang="scss">
.statistics-page {
  background: #f7f7f7;
  min-height: 100vh;
  padding-bottom: 32rpx;
}
.uni-nav-bar {
}
.date-tabs {
  display: flex;
  justify-content: flex-end;
  background: #fff;
  padding: 0 24rpx;
  .tab {
    margin-left: 24rpx;
    font-size: 30rpx;
    color: #888;
    padding: 12rpx 24rpx;
    border-radius: 8rpx;
    &.active {
      background: #1976d2;
      color: #fff;
    }
  }
}
.chart-section {
  background: #fff;
  margin: 24rpx;
  border-radius: 12rpx;
  padding: 12rpx 0 0 0;
}
.pie-section {
  display: flex;
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  border-radius: 12rpx;
  padding: 24rpx 0;
  .pie-left {
    width: 160rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .pie-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .pie-row {
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;
      .pie-label {
        color: #333;
        width: 120rpx;
      }
      .pie-value {
        color: #1976d2;
        font-weight: bold;
        margin: 0 8rpx;
      }
      .pie-rate {
        color: #888;
        font-size: 26rpx;
      }
    }
  }
}
.card-section {
  display: flex;
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  border-radius: 12rpx;
  padding: 24rpx 0;
  .card-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    .card-num {
      font-size: 40rpx;
      font-weight: bold;
      margin-bottom: 8rpx;
      &.green {
        color: #4caf50;
      }
      &.blue {
        color: #1976d2;
      }
    }
    .card-label {
      color: #888;
      font-size: 26rpx;
    }
  }
}
.income-section {
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  border-radius: 12rpx;
  .income-bar {
    display: flex;
    .income-tab {
      flex: 1;
      text-align: center;
      padding: 18rpx 0;
      font-size: 30rpx;
      color: #888;
      &.active {
        color: #1976d2;
        font-weight: bold;
      }
      .income-num {
        color: #1976d2;
        font-weight: bold;
        margin-left: 8rpx;
      }
    }
  }
  .income-list {
    .income-row {
      display: flex;
      align-items: center;
      font-size: 28rpx;
      padding: 12rpx 0;
      border-bottom: 1px solid #f0f0f0;
      .income-date { width: 120rpx; color: #888; }
      .income-type { width: 140rpx; color: #333; }
      .income-car { width: 140rpx; color: #1976d2; }
      .income-amount { width: 120rpx; color: #ff9900; font-weight: bold; }
      .income-action { color: #1976d2; margin-left: 12rpx; }
    }
  }
}
.table-section {
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  border-radius: 12rpx;
  .table-header, .table-row {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    padding: 12rpx 0;
    .th, .td {
      text-align: center;
      flex: 1;
    }
    .rank { color: #1976d2; font-weight: bold; }
    .name { flex: 2; }
    .compare.up { color: #4caf50; }
    .compare.down { color: #ff3b30; }
    .icon.up { color: #4caf50; margin-left: 4rpx; }
    .icon.down { color: #ff3b30; margin-left: 4rpx; }
  }
  .table-header {
    background: #f7f7f7;
    font-weight: bold;
  }
}
</style> 