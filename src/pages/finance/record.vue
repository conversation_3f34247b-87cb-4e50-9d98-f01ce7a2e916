<template>
  <view class="finance-record">
    <!-- 顶部导航栏 -->
    <uni-nav-bar
      :border="false"
      title="财务记录"
      color="#fff"
      left-icon="left"
      backgroundColor="#1976d2"
      @clickLeft="handleBack"
    ></uni-nav-bar>

    <!-- 标签页 -->
    <view class="tabs">
      <view
        v-for="(tab, idx) in tabs"
        :key="tab"
        :class="['tab', { active: currentTab === idx }]"
        @click="currentTab = idx"
      >
        {{ tab }}
      </view>
    </view>

    <!-- 列表 -->
    <scroll-view scroll-y class="record-list">
      <view
        v-for="(item, idx) in recordList"
        :key="idx"
        class="record-card"
      >
        <view class="row">
          <text>时间：{{ item.time }}</text>
          <text>记录分类：{{ item.category }}</text>
        </view>
        <view class="row">
          <text>
            状态：
            <text :class="['status', item.statusColor]">{{ item.statusText }}</text>
          </text>
          <text>关联信息：{{ item.info }}</text>
        </view>
        <view class="row">
          <text>
            金额：
            <text class="amount">{{ item.amount }}</text>
          </text>
        </view>
        <view class="row">
          <text>操作：</text>
          <text
            v-for="(op, i) in item.actions"
            :key="i"
            class="action"
            @click="handleAction(op, item)"
          >{{ op }}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const tabs = ['总览', '已收', '未收', '支出', '未付'];
const currentTab = ref(0);

const recordList = ref([
  {
    time: '2025.06.08 13:29',
    category: '快修保养',
    statusText: '已收款',
    statusColor: 'green',
    info: '川J1C78C0F',
    amount: '553240',
    actions: ['查看'],
  },
  {
    time: '2025.06.08 13:29',
    category: '快修保养',
    statusText: '未收款',
    statusColor: 'red',
    info: '川J1C78C0F',
    amount: '553240',
    actions: ['收款', '查看'],
  },
  {
    time: '2025.06.08 13:29',
    category: '记支出',
    statusText: '发工资',
    statusColor: 'orange',
    info: '张敏',
    amount: '553240',
    actions: ['查看'],
  },
  {
    time: '2025.06.08 13:29',
    category: '配件采购',
    statusText: '未付款',
    statusColor: 'red',
    info: '张敏',
    amount: '553240',
    actions: ['付款'],
  },
  {
    time: '2025.06.08 13:29',
    category: '记支出',
    statusText: '记支出',
    statusColor: 'orange',
    info: '张敏',
    amount: '553240',
    actions: ['查看'],
  },
  {
    time: '2025.06.08 13:29',
    category: '记收入',
    statusText: '记收入',
    statusColor: 'green',
    info: '张敏',
    amount: '553240',
    actions: ['查看'],
  },
]);

function handleBack() {
  uni.navigateBack();
}

function handleAction(action: string, item: any) {
  uni.showToast({ title: `点击了${action}`, icon: 'none' });
}
</script>

<style scoped lang="scss">
.finance-record {
  background: #f7f7f7;
  min-height: 100vh;
}
.tabs {
  display: flex;
  background: #fff;
  border-bottom: 1px solid #eee;
  .tab {
    flex: 1;
    text-align: center;
    padding: 14rpx 0;
    font-size: 30rpx;
    color: #666;
    position: relative;
    &.active {
      color: #ff9900;
      font-weight: bold;
    }
    &.active::after {
      content: '';
      display: block;
      width: 60rpx;
      height: 4rpx;
      background: #ff9900;
      border-radius: 2rpx;
      margin: 0 auto;
      position: absolute;
      left: 0; right: 0; bottom: 0;
    }
  }
}
.record-list {
  padding: 24rpx 0 0 0;
}
.record-card {
  background: #fff;
  border-radius: 12rpx;
  margin: 0 24rpx 24rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10rpx;
    .status {
      margin-left: 8rpx;
      font-weight: bold;
    }
    .amount {
      color: #ff6600;
      font-size: 32rpx;
      font-weight: bold;
      margin-left: 8rpx;
    }
    .action {
      color: #1976d2;
      margin-left: 16rpx;
      cursor: pointer;
    }
    .action:first-child {
      margin-left: 0;
    }
    .green {
      color: #1abc1a;
    }
    .red {
      color: #ff3b30;
    }
    .orange {
      color: #ff9900;
    }
  }
}
</style> 