<template>
  <view class="add-record">
    <!-- 顶部导航栏 -->
    <uni-nav-bar
      :border="false"
      title="新增记账"
      color="#fff"
      left-icon="left"
      backgroundColor="#1976d2"
      @clickLeft="handleBack"
    />

    <!-- 类型切换 -->
    <view class="type-tabs">
      <view
        v-for="(tab, idx) in typeTabs"
        :key="tab.value"
        :class="['tab', { active: type === tab.value }]"
        @click="type = tab.value"
      >
        {{ tab.label }}
      </view>
    </view>

    <!-- 表单内容 -->
    <view class="form-section">
      <!-- 员工选择，仅发工资显示 -->
      <view v-if="type === 'salary'" class="form-row">
        <text class="label">员工：</text>
        <text class="value emp-name">{{ employee }}</text>
        <button class="switch-btn" @click="switchEmployee">切换员工</button>
      </view>
      <!-- 金额输入 -->
      <view class="form-row">
        <text class="label">金额：</text>
        <input
          v-model="amount"
          type="number"
          class="amount-input native-input"
          placeholder="请输入金额"
        />
        <text class="unit">元</text>
      </view>
      <!-- 时间选择 -->
      <view class="form-row">
        <text class="label">时间：</text>
        <template v-if="type === 'salary'">
          <picker mode="date" :value="dateRange[0]" @change="(e: any) => onDateChange(e, 0)">
            <view class="date-picker">{{ dateRange[0] }}</view>
          </picker>
          <text style="margin: 0 8rpx;">至</text>
          <picker mode="date" :value="dateRange[1]" @change="(e: any) => onDateChange(e, 1)">
            <view class="date-picker">{{ dateRange[1] }}</view>
          </picker>
        </template>
        <template v-else>
          <picker mode="date" :value="date" @change="(e: any) => onDateChange(e)">
            <view class="date-picker">{{ date }}</view>
          </picker>
          <picker mode="time" :value="time" @change="(e: any) => onTimeChange(e)">
            <view class="date-picker">{{ time }}</view>
          </picker>
        </template>
      </view>
      <!-- 方式选择 -->
      <view class="form-row">
        <text class="label">方式：</text>
        <view class="way-group">
          <view
            v-for="w in ways"
            :key="w"
            :class="['way-btn', { active: way === w }]"
            @click="way = w"
          >
            {{ w }}
          </view>
        </view>
      </view>
      <!-- 备注输入 -->
      <view class="form-row remark-row">
        <text class="label">备注：</text>
        <textarea
          v-model="remark"
          class="remark-input native-textarea"
          placeholder="请输入备注"
          rows="3"
        />
      </view>
    </view>

    <!-- 说明区域 -->
    <view class="desc-section">
      <text class="desc-title">说明：</text>
      <view class="desc-list">
        <text>1. 记收支功能是便于门店临时产生额资金变动时，用于记账使用，所记录的资金变动纳入财务统计。</text>
        <text>2. 众多门店在员工工资发放管理中，存在争议、不便于记账、利润核算不准确等诸多问题，该数据将作为门店毛利计算的一个重要数据，便于门店参考。</text>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer-btn">
      <button
        class="footer-btn-main"
        type="button"
        @click="submit"
        shape="circle"
      >确定记账</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const typeTabs = [
  { label: '记收入', value: 'income' },
  { label: '记支出', value: 'expense' },
  { label: '发工资', value: 'salary' },
];
const type = ref('income');
const amount = ref('');
const employee = ref('刘民德');
const date = ref('2025.05.22');
const time = ref('13:35');
const dateRange = ref(['2025.05.22', '2025.05.22']);
const way = ref('微信');
const ways = ['微信', '支付宝', '现金', '其他'];
const remark = ref('');

function handleBack() {
  uni.navigateBack();
}
function switchEmployee() {
  uni.showToast({ title: '切换员工', icon: 'none' });
}
function onDateChange(e: { detail: { value: string } }, idx?: number) {
  if (type.value === 'salary' && typeof idx === 'number') {
    dateRange.value[idx] = e.detail.value;
  } else {
    date.value = e.detail.value;
  }
}
function onTimeChange(e: { detail: { value: string } }) {
  time.value = e.detail.value;
}
function submit() {
  uni.showToast({ title: '记账成功', icon: 'success' });
}
</script>

<style scoped lang="scss">
.add-record {
  background: #f7f7f7;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.uni-nav-bar {
  /* 保证顶部栏样式 */
}
.type-tabs {
  display: flex;
  background: #fff;
  border-bottom: 1px solid #eee;
  .tab {
    flex: 1;
    text-align: center;
    padding: 24rpx 0;
    font-size: 32rpx;
    color: #666;
    position: relative;
    &.active {
      color: #1976d2;
      font-weight: bold;
      &::after {
        content: '';
        display: block;
        width: 60rpx;
        height: 4rpx;
        background: #1976d2;
        border-radius: 2rpx;
        margin: 0 auto;
        position: absolute;
        left: 0; right: 0; bottom: 0;
      }
    }
  }
}
.form-section {
  background: #fff;
  margin: 24rpx;
  border-radius: 12rpx;
  padding: 24rpx 0 0 0;
  .form-row {
    display: flex;
    align-items: center;
    padding: 0 24rpx 24rpx 24rpx;
    font-size: 30rpx;
    .label {
      width: 120rpx;
      color: #333;
      flex-shrink: 0;
    }
    .value {
      color: #1976d2;
      margin-right: 16rpx;
    }
    .emp-name {
      font-weight: bold;
    }
    .switch-btn {
      margin-left: 16rpx;
      font-size: 26rpx;
      color: #1976d2;
      background: #e3f0ff;
      border-radius: 8rpx;
      padding: 6rpx 18rpx;
    }
    .amount-input {
      flex: 1;
      margin: 0 8rpx;
    }
    .unit {
      color: #888;
      margin-left: 8rpx;
    }
    .date-picker {
      display: inline-block;
      min-width: 120rpx;
      padding: 0 8rpx;
      color: #1976d2;
      background: #f3f8ff;
      border-radius: 8rpx;
      margin-right: 8rpx;
      text-align: center;
    }
    .way-group {
      display: flex;
      gap: 16rpx;
      .way-btn {
        padding: 8rpx 28rpx;
        border-radius: 8rpx;
        background: #f3f8ff;
        color: #1976d2;
        font-size: 28rpx;
        &.active {
          background: #1976d2;
          color: #fff;
        }
      }
    }
    &.remark-row {
      align-items: flex-start;
      .remark-input {
        flex: 1;
        min-height: 80rpx;
        margin-left: 8rpx;
        border: 1px solid #eee;
        border-radius: 8rpx;
        padding: 12rpx;
        font-size: 28rpx;
        background: #fafbfc;
      }
    }
  }
}
.desc-section {
  margin: 24rpx;
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  .desc-title {
    font-weight: bold;
    color: #1976d2;
    margin-bottom: 12rpx;
    display: block;
  }
  .desc-list {
    color: #888;
    font-size: 26rpx;
    text-indent: 2em;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
  }
}
.footer-btn {
  position: fixed;
  left: 0; right: 0; bottom: 0;
  background: #fff;
  padding: 24rpx 24rpx 48rpx 24rpx;
  z-index: 10;
}
.native-input {
  flex: 1;
  margin: 0 8rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
  padding: 12rpx;
  font-size: 28rpx;
  background: #fafbfc;
}
.native-textarea {
  flex: 1;
  min-height: 80rpx;
  margin-left: 8rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
  padding: 12rpx;
  font-size: 28rpx;
  background: #fafbfc;
}
.footer-btn-main {
  width: 100%;
  height: 90rpx;
  background-color: #1976d2;
  color: #fff;
  border-radius: 12rpx;
  font-size: 36rpx;
  font-weight: bold;
  letter-spacing: 4rpx;
  text-align: center;
  line-height: 90rpx;
  border: none;
}
</style> 