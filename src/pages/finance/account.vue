<template>
  <view class="account-manage">
    <!-- 顶部导航栏 -->
    <uni-nav-bar
      :border="false"
      title="记账管理"
      color="#fff"
      left-icon="left"
      backgroundColor="#1976d2"
      @clickLeft="handleBack"
      fixed
    ></uni-nav-bar>

    <!-- 账单列表 -->
    <scroll-view scroll-y class="account-list">
      <uni-swipe-action>
        <uni-swipe-action-item
          v-for="(item, idx) in accountList"
          :key="idx"
          :right-options="swipeOptions"
          @click="({ content }) => handleSwipeAction(content, idx)"
        >
          <view class="account-card">
            <view class="row top-row">
              <view class="type-icon" :class="item.typeColor">
                <text class="iconfont" :class="item.icon"></text>
                <text class="type-text">{{ item.typeText }}</text>
              </view>
              <view class="time">{{ item.time }}</view>
            </view>
            <view class="row">
              <text>{{ item.amountLabel }}：</text>
              <text class="amount">{{ item.amount }}</text>
              <text class="way-label">入账方式：</text>
              <text :class="['way', item.wayColor]">{{ item.way }}</text>
            </view>
            <view class="row remark-row">
              <text>备注说明：</text>
              <text class="remark">{{ item.remark }}</text>
            </view>
          </view>
        </uni-swipe-action-item>
      </uni-swipe-action>
    </scroll-view>

    <!-- 新增按钮 -->
    <view class="add-btn" @click="addAccount">新增记账</view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";

const accountList = ref([
  {
    type: "income",
    typeText: "计收入",
    typeColor: "blue",
    icon: "icon-income",
    time: "2025.05.22 13:35",
    amountLabel: "入账金额",
    amount: 217,
    way: "微信",
    wayColor: "green",
    remark: "无",
  },
  {
    type: "expense",
    typeText: "计支出",
    typeColor: "red",
    icon: "icon-expense",
    time: "2025.05.22 13:35",
    amountLabel: "支出金额",
    amount: 217,
    way: "支付宝",
    wayColor: "blue",
    remark: "李敏请客户张三中午吃了个午饭，一个卤香肉丝一个宫保鸡丁",
  },
  {
    type: "salary",
    typeText: "发工资",
    typeColor: "orange",
    icon: "icon-salary",
    time: "2025.05.22 13:35",
    amountLabel: "发放金额",
    amount: 217,
    way: "其他",
    wayColor: "gray",
    remark: "无",
  },
]);

const swipeOptions = [
  {
    text: "编辑",
    style: {
      backgroundColor: "#1976d2",
      color: "#fff",
      fontWeight: "bold",
      width: "120rpx",
    },
  },
  {
    text: "删除",
    style: {
      backgroundColor: "#ff9900",
      color: "#fff",
      fontWeight: "bold",
      width: "120rpx",
    },
  },
];

function handleBack() {
  uni.navigateBack();
}
function handleSwipeAction(content: string, idx: number) {
  if (content === "编辑") {
    uni.showToast({ title: "编辑功能", icon: "none" });
  } else if (content === "删除") {
    uni.showModal({
      title: "提示",
      content: "确定要删除这条记账吗？",
      success: (res) => {
        if (res.confirm) {
          accountList.value.splice(idx, 1);
          uni.showToast({ title: "已删除", icon: "success" });
        }
      },
    });
  }
}
function addAccount() {
  uni.showToast({ title: "新增记账", icon: "none" });
}
</script>

<style scoped lang="scss">
@import url("//at.alicdn.com/t/font_1234567_xxxxxxx.css"); /* 替换为你的iconfont地址 */

.account-manage {
  background: #f7f7f7;
  min-height: 100vh;
  position: relative;
  padding-bottom: 120rpx;
}
.uni-nav-bar {
  /* 保证顶部栏样式 */
}
.account-list {
  padding: 24rpx 0 0 0;
}
.account-card {
  background: #fff;
  border-radius: 12rpx;
  margin: 0 24rpx 24rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  .row {
    display: flex;
    align-items: center;
    margin-bottom: 10rpx;
    &.top-row {
      justify-content: space-between;
      margin-bottom: 8rpx;
      .type-icon {
        display: flex;
        align-items: center;
        font-weight: bold;
        .iconfont {
          font-size: 32rpx;
          margin-right: 8rpx;
        }
        &.blue {
          color: #1976d2;
        }
        &.red {
          color: #ff3b30;
        }
        &.orange {
          color: #ff9900;
        }
      }
      .time {
        color: #888;
        font-size: 26rpx;
      }
    }
    .amount {
      color: #ff6600;
      font-size: 32rpx;
      font-weight: bold;
      margin: 0 8rpx;
    }
    .way-label {
      margin-left: 16rpx;
    }
    .way {
      margin-left: 4rpx;
      &.green {
        color: #1abc1a;
      }
      &.blue {
        color: #1976d2;
      }
      &.gray {
        color: #888;
      }
    }
    .remark-row {
      align-items: flex-start;
      .remark {
        color: #888;
        margin-left: 8rpx;
        word-break: break-all;
      }
    }
  }
}
.add-btn {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #1976d2;
  color: #fff;
  text-align: center;
  font-size: 32rpx;
  padding: 28rpx 0;
  font-weight: bold;
  letter-spacing: 2rpx;
}
</style>
