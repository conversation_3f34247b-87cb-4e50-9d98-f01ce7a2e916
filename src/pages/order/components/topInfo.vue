<template>
  <scroll-view scroll-y class="top-dialog" @scrolltolower="loadMore">
    <view class="info-form" v-if="topType === 'customerInfo'">
      <!-- 车主姓名 -->
      <view class="form-item">
        <text class="label">车主姓名：</text>
        <text class="value">{{ info.fullname }}</text>
      </view>

      <!-- 联系电话和车主来源 -->
      <view class="form-item contact-row">
        <view class="contact">
          <text class="label">联系电话：</text>
          <text class="value">{{ info.phone }}</text>
        </view>
        <view class="source">
          <text class="label">车主来源：</text>
          <view class="value-with-arrow">
            <text>{{
              info.customer_source === 1
                ? "朋友推荐"
                : info.customer_source === 2
                ? "普通客户"
                : "通讯录"
            }}</text>
          </view>
        </view>
      </view>

      <!-- 会员套餐和折扣 -->
      <view class="form-item member-row">
        <view class="member-plan">
          <text class="label">会员套餐：</text>
          <text class="value">{{ info.is_vip === 1 ? "无" : "有" }}</text>
        </view>
        <view class="member-discount">
          <text class="label">会员折扣：</text>
          <text class="value">9.5 折</text>
        </view>
      </view>

      <!-- 车主生日 -->
      <view class="form-item">
        <text class="label">车主生日：</text>
        <text class="value">{{ info.birthday }}</text>
      </view>

      <!-- 车主地址 -->
      <view class="form-item">
        <text class="label">车主地址：</text>
        <text class="value"></text>
      </view>

      <!-- 客户备注 -->
      <view class="form-item remark">
        <text class="label">客户备注：</text>
        <view class="remark-content">
          <text class="value">{{ info.note }}</text>
          <image
            class="edit-icon"
            src="/static/images/order/order_edit.png"
            mode="aspectFit"
          />
        </view>
      </view>
    </view>
    <view class="car-list" v-else-if="topType === 'carInfo'">
      <!-- 车辆信息卡片列表 -->
      <view class="car-cards">
        <view class="car-card" v-for="(car, index) in car_info" :key="index">
          <view class="info-item">
            <text class="label">车牌号码：</text>
            <text class="value">{{ car.car_license_code }}</text>
          </view>
          <view class="info-item">
            <text class="label">车辆VIN：</text>
            <text class="value">{{ car.car_vin_code }}</text>
          </view>
          <view class="info-item">
            <text class="label">车型品牌：</text>
            <text class="value brand">{{ car.car_brand }}</text>
          </view>
          <view class="info-item">
            <text class="label">保险公司：</text>
            <text class="value">{{ car.insurance_company }}</text>
          </view>
          <view class="info-item">
            <text class="label">保险到期：</text>
            <text class="value">{{ car.insurance_end_date }}</text>
          </view>
          <view class="info-item">
            <text class="label">最新里程：</text>
            <text class="value">{{ car.cur_mileage }} 公里</text>
          </view>
        </view>
      </view>

      <!-- 添加车辆按钮 -->
      <view class="add-car" @click="onAddCar">
        <image
          class="add-icon"
          src="/static/images/order/add_user.png"
          mode="aspectFit"
        />
      </view>
    </view>
    <!-- 上次到店信息 -->
    <view class="repair-list" v-else>
      <!-- 上次到店信息 -->
      <view class="last-visit">
        <view class="visit-row">
          <text class="label">上次到店：</text>
          <text class="value">{{ last_time }}</text>
        </view>
        <view class="visit-row">
          <text class="label">累计消费：</text>
          <text class="value price">{{ count_money }}元</text>
        </view>
      </view>

      <!-- 维修记录列表 -->
      <view class="record-list">
        <view
          class="record-item"
          v-for="(record, index) in records"
          :key="index"
        >
          <view class="record-row flex-sbw">
            <view class="time">
              <text>{{ record.create_time }}</text>
            </view>
            <view class="mileage">
              <text>订单金额：</text>
              <text class="time">{{ record.order_money }}</text>
            </view>
          </view>
          <view class="record-row flex-sbw">
            <view>
              <text>送修里程：</text>
              <text>{{ record.cur_mileage }}</text>
            </view>
            <view class="mileage">
              <text>详情</text>
              <uni-icons class="arrow" type="right"></uni-icons>
            </view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getOrderHistory } from "@/api/order";
import type { Response } from "@/utils/type";
interface CarInfo {
  id: number; // 车辆ID
  company_id: number;
  customer_id: number; // 客户ID
  car_license_code: string; // 车牌号
  car_vin_code: string; // 车辆VIN号
  car_brand: string; // 车辆品牌
  cur_mileage: number; // 当前里程 KM
  insurance_company: string | null; // 保险公司
  insurance_end_date: string | null; // 保险到期日期
  vehicleusecharacter: string; // 车辆使用性质(01:营运02:非营运)
  vehicleowner: string; // 车辆所有人
  enginecode: string; // 发动机号
  next_mileage: number | null; // 下次保养里程 KM
  next_date: string | null; // 下次保养日期
}
interface Info {
  id: number; // 客户ID
  phone: string; // 客户手机号
  fullname: string; // 客户姓名
  face: string; // 客户头像
  customer_source: number; // 客户来源 1朋友推荐 2普通客户 3通讯录
  note: string; // 客户备注
  is_vip: number; // 是否是会员 0 否 1是
  birthday: string; // 客户生日
}
interface propsData {
  topType: string;
  info: Info;
  car_info: CarInfo[];
  car_id: number;
}
const { topType, info, car_info, car_id } = defineProps<propsData>();
const records = ref();
const onAddCar = () => {
  console.log(222);
};
const page = ref(1);
const count = ref(0);
const limit = ref(10);
const count_money = ref("");
const last_time = ref("");
const getOrderHistoryData = async () => {
  const res: Response = (await getOrderHistory({
    page: page.value,
    limit: limit.value,
    is_show_all: 0, // 是否显示总记录数 0否 1是 非必要情况请使用0
    car_id, // 车辆id
  })) as Response;
  if (res.code === 200) {
    if (page.value === 1) {
      records.value = res.data.list;
      count.value = res.data.count;
      count_money.value = res.data.count_money;
      last_time.value = res.data.last_time;
    } else {
      records.value = [...records.value, ...res.data.list];
    }
  } else {
    records.value = [];
  }
};
const loadMore = () => {
  if (topType === "history") {
    page.value++;
    if (page.value > Math.ceil(count.value / limit.value)) {
      return;
    } else {
      getOrderHistoryData();
    }
  }
};
onMounted(() => {
  if (topType === "history") {
    getOrderHistoryData();
  }
});
</script>

<style scoped lang="scss">
.top-dialog {
  background-color: #fff;
  padding: 32rpx;
  height: 60vh;
  box-sizing: border-box;
  .info-form {
    .form-item {
      margin-bottom: 32rpx;
      font-size: 28rpx;
      line-height: 40rpx;

      .label {
        color: #666;
        display: inline-block;
        width: 140rpx;
      }

      .value {
        color: #333;
      }

      &.contact-row,
      &.member-row {
        display: flex;
        justify-content: space-between;

        .contact,
        .member-plan {
          flex: 1;
        }

        .source,
        .member-discount {
          flex: 1;

          .value-with-arrow {
            display: inline-flex;
            align-items: center;
          }
        }
      }

      &.remark {
        .remark-content {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          background-color: #f8f8f8;
          padding: 24rpx 24rpx 0 24rpx;
          margin-top: 16rpx;
          border-radius: 8rpx;
          min-height: 180rpx;

          .value {
            flex: 1;
            margin-right: 16rpx;
          }

          .edit-icon {
            width: 32rpx;
            height: 32rpx;
            flex-shrink: 0;
          }
        }
      }
    }
  }
  .car-list {
    .car-cards {
      .car-card {
        border-radius: 16rpx;
        padding: 24rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);

        .info-item {
          display: flex;
          font-size: 28rpx;
          line-height: 2;

          .label {
            width: 160rpx;
            color: #666;
            flex-shrink: 0;
          }

          .value {
            flex: 1;
            color: #333;
            &.brand {
              width: calc(100% - 160rpx);
              // 单行文本超出省略
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }
          }
        }
      }
    }

    .add-car {
      background-color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0 0;

      .add-icon {
        width: 44rpx;
        height: 44rpx;
      }

      &:active {
        opacity: 0.8;
      }
    }
  }
  .repair-list {
    .last-visit {
      background-color: #fff;
      border-radius: 12rpx;
      padding: 24rpx;
      margin-bottom: 24rpx;

      .visit-row {
        display: flex;
        justify-content: space-between;
        font-size: 30rpx;
        line-height: 48rpx;

        .label {
          color: #666;
        }

        .value {
          color: #333;

          &.price {
            color: var(--primary-color);
          }
        }
      }
    }

    .record-list {
      .record-item {
        color: #515151;
        font-size: 26rpx;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
        padding: 24rpx;
        border-radius: 12rpx;
        margin-bottom: 24rpx;
        line-height: 2;

        .record-row {
          .time {
            color: var(--primary-color);
          }
          .arrow {
            margin-left: 16rpx;
          }
        }
      }
    }
  }
}
</style>
