<template>
  <view class="settlement-popup">
    <!-- 费用信息 -->
    <view class="fee-section">
      <view class="fee-row">
        <text class="label">服务收费：</text>
        <text class="amount member">¥100.00</text>
        <text class="label margin-left">配件总价：</text>
        <text class="amount member">¥21050.00</text>
      </view>
      <view class="fee-row">
        <text class="label">合计应收：</text>
        <text class="amount member">¥21150.00</text>
        <text class="label margin-left">会员身份：</text>
        <text class="member">会员(1)</text>
      </view>
    </view>
    <view class="package-section">
      <view class="package-row">
        <text class="label">套餐折扣：</text>
        <text class="amount member">320洗车扣次套餐[次卡]</text>
      </view>
      <view class="package-row">
        <text class="label">套餐优惠：</text>
        <text class="amount member">扣除一次[剩余16次]</text>
      </view>
    </view>
    <!-- 支付信息 -->
    <view class="payment-section">
      <view class="payment-row">
        <text class="label">实际应付：</text>
        <text class="amount highlight">¥21150.00</text>
        <text class="label margin-left">抹零优惠：</text>
        <text class="amount">无</text>
      </view>

      <!-- 实收金额 -->
      <view class="actual-payment">
        <text class="label">实收金额：</text>
        <text class="amount highlight">¥1150.00</text>
        <view class="radio-group">
          <uni-data-checkbox
            v-model="paymentType"
            :localdata="paymentTypeList"
          ></uni-data-checkbox>
        </view>
      </view>

      <!-- 收款方式 -->
      <view class="payment-method">
        <text class="label">收款方式：</text>
        <view class="radio-group">
          <uni-data-checkbox
            v-model="payMethod"
            :localdata="payMethodList"
          ></uni-data-checkbox>
        </view>
      </view>

      <!-- 挂账金额 -->
      <view class="credit-amount">
        <text class="label">挂账金额：</text>
        <text class="amount highlight">¥20000.00</text>
      </view>
    </view>

    <!-- 确定按钮 -->
    <view class="submit-section">
      <button class="submit-btn" form-type="submit" @click="handleSubmit">
        确定
      </button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref } from "vue";

// 支付类型（挂账/发给车主）
const paymentType = ref("1");
// 支付方式
const payMethod = ref("wechat");
const payMethodList = ref([
  {
    value: "wechat",
    text: "微信",
  },
  {
    value: "alipay",
    text: "支付宝",
  },
  {
    value: "cash",
    text: "现金",
  },
  {
    value: "card",
    text: "银行卡",
  },
]);
const paymentTypeList = ref([
  {
    value: "1",
    text: "挂账",
  },
  {
    value: "2",
    text: "发给车主",
  },
]);

// 显示帮助信息
const showHelp = () => {
  uni.showToast({
    title: "帮助信息",
    icon: "none",
  });
};

// 提交支付
const handleSubmit = () => {
  uni.showLoading({
    title: "处理中",
  });

  setTimeout(() => {
    uni.hideLoading();
    uni.showToast({
      title: "支付成功",
      icon: "success",
    });
  }, 1500);
};
</script>

<style lang="scss" scoped>
.settlement-popup {
  background-color: #fff;
  padding: 30rpx;

  .fee-section,
  .package-section,
  .payment-section {
    margin-bottom: 30rpx;
    .package-row {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
    }
  }

  .fee-row {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .label {
    font-size: 28rpx;
    color: #666;
  }

  .amount {
    font-size: 28rpx;
    color: #333;

    &.highlight {
      color: #ff4d4f;
      font-weight: 500;
    }
  }

  .member {
    font-size: 28rpx;
    color: #1890ff;
  }

  .margin-left {
    margin-left: auto;
  }

  .payment-section {
    .payment-row,
    .actual-payment,
    .payment-method,
    .credit-amount {
      margin-bottom: 30rpx;
      display: flex;
      align-items: center;
    }

    .radio-group {
      margin-left: auto;
      display: flex;
      align-items: center;
      gap: 20rpx;
    }
  }

  :deep(.checklist-box) {
    margin-right: 20rpx !important;
  }

  :deep(.uni-list-item) {
    padding: 20rpx 0;
  }

  :deep(.uni-list-item__content-title) {
    font-size: 28rpx !important;
    color: #666 !important;
  }

  :deep(.uni-list-item__extra-text) {
    font-size: 28rpx !important;
    color: #ff6b35 !important;
  }

  .submit-section {
    margin-top: 40rpx;

    .submit-btn {
      width: 100%;
      height: 88rpx;
      line-height: 88rpx;
      background-color: var(--primary-color);
      color: #fff;
      font-size: 32rpx;
      border-radius: 44rpx;
      text-align: center;
    }
  }
}
</style>
