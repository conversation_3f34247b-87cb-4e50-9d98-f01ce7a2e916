<template>
  <view class="send-work">
    <!-- 服务管理 -->
    <scroll-view
      scroll-y
      class="dispatch-popup"
      v-if="currentType === 'service'"
    >
      <!-- 表头 -->
      <view class="table-header">
        <text class="header-item name">名称</text>
        <text class="header-item work">小计</text>
        <text class="header-item work"> 操作 </text>
      </view>

      <!-- 表格内容 -->
      <view class="table-content">
        <view class="table-row" v-for="(item, index) in staffList" :key="index">
          <text class="row-item name">{{ item.name }}</text>
          <text class="row-item work">{{ item.workCount }}</text>
          <text class="header-item work">
            <uni-icons type="trash" size="20" color="#2979ff" />
          </text>
        </view>
        <view class="add-button" @click="handleAdd">
          <uni-icons type="plus" size="30" color="#2979ff" />
        </view>
      </view>
      <!-- 添加按钮 -->
      <view class="bottom-button">
        <button class="submit-btn" @click="handleSubmit">确定</button>
      </view>
    </scroll-view>
    <!-- 配件管理 -->
    <view class="dispatch-popup" v-else-if="currentType === 'accessory'">
      <!-- 表头 -->
      <view class="table-header">
        <text class="header-item name">名称</text>
        <text class="header-item work">数量</text>
        <text class="header-item work">单价</text>
        <text class="header-item work">小计</text>
      </view>

      <!-- 表格内容 -->
      <scroll-view scroll-y class="table-content">
        <view class="table-row" v-for="(item, index) in staffList" :key="index">
          <text class="row-item name">{{ item.name }}</text>
          <text class="row-item work">{{ item.workCount }}</text>
          <text class="row-item work">{{ item.workCount }}</text>
          <text class="row-item work">{{ item.workCount }}</text>
        </view>
      </scroll-view>

      <!-- 添加按钮 -->
      <view class="add-button" @click="handleAdd">
        <uni-icons type="plus" size="30" color="#2979ff" />
      </view>
      <view class="bottom-button">
        <button class="submit-btn" @click="handleSubmit">确定</button>
      </view>
    </view>
    <!-- 派工管理 -->
    <view class="dispatch-popup" v-else-if="currentType === 'dispatch'">
      <!-- 表头 -->
      <view class="table-header">
        <text class="header-item name">姓名</text>
        <text class="header-item work">工作项</text>
        <text class="header-item work">划工时</text>
      </view>
      <!-- 表格内容 -->
      <scroll-view scroll-y class="table-content">
        <view class="table-row" v-for="(item, index) in staffList" :key="index">
          <text class="row-item name">{{ item.name }}</text>
          <text class="row-item work">{{ item.workCount }}</text>
        </view>
        <!-- 添加按钮 -->
        <view class="add-button" @click="handleAdd">
          <uni-icons type="plus" size="30" color="#2979ff" />
        </view>
      </scroll-view>
      <view class="bottom-button">
        <button class="submit-btn" @click="handleSubmit">确定</button>
      </view>
    </view>
    <!-- 施工管理 -->
    <view class="dispatch-popup" v-else-if="currentType === 'sendWork'">
      <!-- 表头 -->
      <view class="table-header">
        <text class="header-item name">姓名</text>
        <text class="header-item work">工作项</text>
        <text class="header-item work">划工时</text>
      </view>

      <!-- 表格内容 -->
      <scroll-view scroll-y class="table-content">
        <view class="table-row" v-for="(item, index) in staffList" :key="index">
          <text class="row-item name">{{ item.name }}</text>
          <text class="row-item work">{{ item.workCount }}</text>
          <text class="row-item work">{{ item.workCount }}</text>
        </view>
        <view class="add-button" @click="handleAdd">
          <uni-icons type="plus" size="30" color="#2979ff" />
        </view>
      </scroll-view>

      <view class="bottom-button">
        <button class="submit-btn" @click="handleSubmit">确定</button>
      </view>
    </view>

    <!-- 完工自检 -->
    <view class="dispatch-popup" v-else-if="currentType === 'quality'">
      <scroll-view scroll-y class="table-content">
        <view class="info-section">
          <picker
            :range="staffList"
            range-key="name"
            @change="handleStaffChange"
          >
            <view class="info-item">
              <text class="label">姓名：</text>
              <text class="value">{{ staffName }}</text>
              <uni-icons type="right" size="16" color="#999" />
            </view>
          </picker>
          <picker :range="workHoursList" @change="handleHoursChange">
            <view class="info-item">
              <text class="label">划工时</text>
              <text class="value orange">{{ workHours }}</text>
              <uni-icons type="right" size="16" color="#999" />
            </view>
          </picker>
        </view>

        <uv-collapse>
          <uv-collapse-item title="工作项" name="workItem">
            <checkbox-group @change="handleCheckboxChange">
              <label
                class="work-item"
                v-for="(item, index) in workItems"
                :key="index"
              >
                <text class="item-name">{{ item.name }}</text>
                <checkbox
                  :value="item.name"
                  :checked="item.selected"
                  color="#ff6b35"
                />
              </label>
            </checkbox-group>
          </uv-collapse-item>
          <uv-collapse-item title="照片" name="photo">
            <view class="photo-list">
              <view class="photo-item">
                <image src="" />
              </view>
            </view>
          </uv-collapse-item>
        </uv-collapse>
        <!-- 工作项列表 -->
      </scroll-view>
      <!-- 底部按钮 -->
      <view class="bottom-button">
        <button class="submit-btn" @click="handleSubmit">确定</button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";

// 员工信息接口
interface Staff {
  name: string;
  workCount: number;
  workHours: number;
}
const { currentType } = defineProps({
  currentType: {
    default: "service",
    require: true,
  },
});

// 员工列表数据
const staffList = ref<Staff[]>([
  { name: "陈超", workCount: 11, workHours: 11 },
  { name: "李忠芳", workCount: 11, workHours: 11 },
  { name: "毛瑶瑶", workCount: 11, workHours: 11 },
  { name: "荷俊", workCount: 11, workHours: 11 },
]);

// 弹窗引用
// 当前选中的员工
const currentStaff = ref<Staff>();

// 打开管理操作弹窗
const handleManage = (staff: Staff) => {
  currentStaff.value = staff;
};

// 添加员工
const handleAdd = () => {
  uni.showToast({
    title: "添加员工",
    icon: "none",
  });
};

// 工时列表数据
const workHoursList = ref([
  "2",
  "4",
  "6",
  "8",
  "10",
  "12",
  "14",
  "16",
  "18",
  "20",
  "22",
  "24",
]);

// 员工姓名
const staffName = ref("陈超");
// 划工时
const workHours = ref("11");

// 工作项列表
const workItems = ref([
  { name: "常规小保养", selected: true },
  { name: "0W40机油更换", selected: true },
  { name: "常规12项筛查", selected: true },
  { name: "空气流量计更换", selected: false },
  { name: "刹车片", selected: true },
  { name: "更换排气管垫片", selected: true },
]);

// 已选择的工作项
const selectedItems = computed(() => {
  return workItems.value.filter((item) => item.selected);
});

// 根据传入的类型，显示不同的表头
const tableHeader = computed(() => {
  const headers = {
    service: [
      { title: "名称", key: "title" },
      { title: "小计", key: "price" },
    ],
    accessory: [
      { title: "名称", key: "title" },
      { title: "数量", key: "num" },
      { title: "单价", key: "price_sale" },
      { title: "小计", key: "amount" },
    ],
    dispatch: [
      { title: "姓名", key: "fullname" },
      { title: "工作项", key: "workCount" },
      { title: "划工时", key: "workHours" },
    ],
    sendWork: [
      { title: "姓名", key: "name" },
      { title: "工作项", key: "workCount" },
      { title: "划工时", key: "workHours" },
    ],
    quality: [
      { title: "姓名", key: "name" },
      { title: "划工时", key: "workHours" },
    ],
  };
  return headers[currentType as keyof typeof headers];
});
// 处理员工选择
const handleStaffChange = (e: { detail: { value: number } }) => {
  const index = e.detail.value;
  staffName.value = staffList.value[index].name;
};

// 处理工时选择
const handleHoursChange = (e: any) => {
  const index = e.detail.value;
  workHours.value = workHoursList.value[index];
};

// 处理复选框变化
const handleCheckboxChange = (e: any) => {
  const selectedValues = e.detail.value;
  workItems.value.forEach((item) => {
    item.selected = selectedValues.includes(item.name);
  });
};

// 提交选择
const handleSubmit = () => {
  uni.showToast({
    title: "保存成功",
    icon: "success",
  });
  // 这里可以添加回调函数，将选择结果传递给父组件
};
</script>

<style lang="scss" scoped>
.dispatch-popup {
  background-color: #fff;
  height: 60vh;
  position: relative;
  padding-bottom: 120rpx;
}

.table-header {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  background-color: #f8f8f8;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.table-content {
  height: calc(100% - 80rpx);

  .work-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;

    .item-name {
      font-size: 28rpx;
      color: #333;
    }
  }

  // 自定义 checkbox 样式
  :deep(.uni-checkbox-input) {
    border-radius: 0;
    color: #ff6b35;
  }
}

.table-row {
  display: flex;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }
}

.header-item,
.row-item {
  &.name {
    flex: 2;
  }

  &.work,
  &.time {
    flex: 1;
    text-align: center;
    // color: #ff6b35;
  }

  &.manage {
    flex: 1;
    display: flex;
    justify-content: center;
  }
}

.row-item {
  font-size: 28rpx;
  color: #333;
}

.add-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
}

.popup-content {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;

  .popup-title {
    text-align: center;
    font-size: 32rpx;
    color: #333;
    margin-bottom: 30rpx;
  }

  .popup-list {
    .popup-item {
      display: flex;
      align-items: center;
      gap: 20rpx;
      padding: 30rpx 0;
      font-size: 30rpx;
      color: #333;
      border-bottom: 1px solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .popup-cancel {
    margin-top: 20rpx;
    text-align: center;
    padding: 30rpx 0;
    font-size: 30rpx;
    color: #666;
    border-top: 1px solid #f5f5f5;
  }
}
.work-item-popup {
  background-color: #fff;
  max-height: 60vh;
  display: flex;
  flex-direction: column;
}

.info-section {
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  picker {
    width: 45%;
  }

  .info-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;

    .label {
      font-size: 28rpx;
      color: #333;
    }

    .value {
      // flex: 1;
      font-size: 28rpx;
      color: #333;
      margin: 0 10rpx;

      &.orange {
        color: #ff6b35;
      }
    }
  }
}

.work-count {
  height: 80rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  justify-content: space-between;

  .label {
    font-size: 28rpx;
    color: #333;
  }

  .value {
    margin-right: auto;
    margin-left: 20rpx;
    font-size: 28rpx;

    &.orange {
      color: #ff6b35;
    }
  }
}

.work-list {
  flex: 1;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.bottom-button {
  // padding: 20rpx 30rpx;

  .submit-btn {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    background-color: var(--primary-color);
    color: #fff;
    font-size: 32rpx;
    border-radius: 0;
    border-radius: 44rpx;
    text-align: center;
  }
}
</style>
