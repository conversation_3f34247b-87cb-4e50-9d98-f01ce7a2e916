<template>
  <uni-nav-bar
    :border="false"
    title="维修订单"
    color="#fff"
    left-icon="left"
    @clickLeft="handleClickLeft"
    backgroundColor="var(--primary-color)"
    fixed
    status-bar
  >
  </uni-nav-bar>
  <view class="order-manage">
    <uv-tabs
      :activeStyle="{
        color: 'var(--warning-color)',
        fontWeight: 'bold',
        transform: 'scale(1.05)',
      }"
      lineColor="var(--warning-color)"
      :list="statusList"
      @click="clickStatus"
    ></uv-tabs>
    <uv-search
      placeholder="请输入搜索内容"
      :showAction="false"
      v-model="keyword"
      @search="search"
    ></uv-search>
    <scroll-view
      @scrolltolower="loadMore"
      class="order-manage-scroll-view"
      scroll-y
    >
      <view
        class="order-item"
        @tap="handleClickOrder(item)"
        v-for="(item, index) in orderList"
        :key="index"
      >
        <view class="order-item-left">
          <image class="car-image" :src="item.face" mode="aspectFill"></image>
        </view>
        <view class="order-item-right">
          <view class="order-item-header">
            <view class="car-number">{{ item.car_license_code }}</view>
            <view class="member-tag" :class="{ 'is-member': item.is_vip }">
              {{ item.is_vip ? "会员" : "非会员" }}
            </view>
            <view class="order-creator">开单人员：{{ item.creator_name }}</view>
          </view>
          <view class="order-item-content">
            <view class="order-remark">
              车主嘱附：{{ item.pickup_note || "无" }}
            </view>
            <view class="order-status" :class="'status-' + item.status">
              {{ statusMap[item.status] }}
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getOrderList } from "@/api/order";
import type { Response } from "@/utils/type";
interface OrderItem {
  car_license_code: string; // 车牌号
  is_vip: number; // 是否是会员 0否 1是
  creator_name: string; // 开单人姓名
  face: string; // 头像图片
  order_state: number; // 真实订单状态  1待开单 2检修中 3待派单 4已派单 5服务中 6待件中 7待验车 8待结算 9待提车 10已提车 11已评价
  status: number; // 前端显示订单状态 0全部订单、1已接车、2服务中、3待结算、4已结算、5待施工、6返修中、7待完工、8待提车
  pickup_note: string; // 车主嘱托
  order_id: number; // 订单ID
  car_id: number; // 车辆ID
  creator_id: number; // 开单人ID
}
/* 
  "order_state": 2, // 真实订单状态  1待开单 2检修中 3待派单 4已派单 5服务中 6待件中 7待验车 8待结算 9待提车 10已提车 11已评价
  "status": 0, // 前端显示订单状态 0全部订单、1已接车、2服务中、3待结算、4已结算、5待施工、6返修中、7待完工、8待提车
*/
enum statusMap {
  "全部订单",
  "已接车",
  "服务中",
  "待结算",
  "已结算",
  "待施工",
  "返修中",
  "待完工",
  "待提车",
}

const statusList = [
  { name: "全部订单", status: 0 },
  { name: "已接车", status: 1 },
  { name: "服务中", status: 2 },
  { name: "待结算", status: 3 },
  { name: "已结算", status: 4 },
  { name: "待施工", status: 5 },
  { name: "返修中", status: 6 },
  { name: "待完工", status: 7 },
  { name: "待提车", status: 8 },
];
const status = ref(0);
const keyword = ref("");
const clickStatus = (item: any) => {
  page.value = 1;
  status.value = item.status;
  keyword.value = "";
  orderList.value = [];
  getOrderListData();
};
const search = () => {
  page.value = 1;
  orderList.value = [];
  getOrderListData();
};

const getStatusClass = (status: string) => {
  switch (status) {
    case "待完工":
      return "status-waiting";
    case "检修中":
      return "status-checking";
    case "返修中":
      return "status-return";
    case "待施工":
      return "status-pending";
    default:
      return "";
  }
};
const page = ref(1);
const limit = ref(20);
const count = ref(0);
const orderList = ref<OrderItem[]>([
  {
    car_license_code: "川A88888", // 车牌号
    is_vip: 0, // 是否是会员 0否 1是
    creator_name: "刘明", // 开单人姓名
    face: "", // 头像图片
    order_state: 3, // 真实订单状态  1待开单 2检修中 3待派单 4已派单 5服务中 6待件中 7待验车 8待结算 9待提车 10已提车 11已评价
    status: 8, // 前端显示订单状态 0全部订单、1已接车、2服务中、3待结算、4已结算、5待施工、6返修中、7待完工、8待提车
    pickup_note: "刹车片使劲检查", // 车主嘱托
    order_id: 97582, // 订单ID
    car_id: 24015, // 车辆ID
    creator_id: 74, // 开单人ID
  },
]);
const getOrderListData = async () => {
  const params = {
    page: page.value,
    limit: limit.value,
    status: status.value,
    keyword: keyword.value,
  };
  const res: Response = (await getOrderList(params)) as Response;
  if (res.code === 200) {
    if (page.value === 1) {
      orderList.value = res.data.list;
      count.value = res.data.count;
    } else {
      orderList.value = [...orderList.value, ...res.data.list];
    }
  }
};

const loadMore = () => {
  page.value++;
  if (page.value > Math.ceil(count.value / limit.value)) {
    return;
  } else {
    getOrderListData();
  }
};
const handleClickLeft = () => {
  uni.navigateBack();
};
const handleClickOrder = (item: OrderItem) => {
  uni.navigateTo({
    url: `/pages/order/orderDetail?id=${item.order_id}`,
  });
};
onMounted(() => {
  getOrderListData();
});
</script>

<style scoped lang="scss">
:deep(.uv-search) {
  padding: 10rpx;
}
.order-manage {
  height: calc(100vh - 44px);
  box-sizing: border-box;
  .order-manage-scroll-view {
    height: calc(100% - 176rpx);
    box-sizing: border-box;
    padding: 0 3.5vw 3.5vw 3.5vw;
    background-color: #f7f7f7;
    .order-manage-item {
      padding: 2vw 0;
      border-bottom: 1px solid #fff;
    }
  }

  .order-item {
    display: flex;
    padding: 20rpx 0;

    &-left {
      margin-right: 20rpx;

      .car-image {
        width: 14.4vw;
        height: 14.4vw;
        border-radius: 1.33vw;
        background-color: #fff;
      }
    }

    &-right {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    &-header {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .car-number {
        font-size: 3.47vw;
        color: #333;
        margin-right: 3.07vw;
      }

      .member-tag {
        font-size: 2.93vw;
        color: #999;

        &.is-member {
          color: #ff6b00;
          background-color: rgba(255, 107, 0, 0.1);
        }
      }

      .order-creator {
        font-size: 2.93vw;
        color: #333;
        margin-left: auto;
      }
    }

    &-content {
      padding-top: 2.67vw;
      display: flex;
      justify-content: space-between;
      .order-remark {
        font-size: 2.93vw;
        color: #666;
        width: 60vw;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      .order-status {
        font-size: 2.93vw;

        &.status-0 {
          color: var(--primary-color);
        }
        &.status-1 {
          color: #098317;
        }

        &.status-2 {
          color: #333333;
        }
        &.status-3 {
          color: #750b88;
        }
        &.status-5 {
          color: #11896b;
        }
        &.status-6 {
          color: #ee0e0e;
        }
        &.status-7 {
          color: #086bdb;
        }
        &.status-8 {
          color: #126342;
        }
      }
    }
  }
}
.order-manage-search {
  display: flex;
  align-items: center;
  justify-content: center;
  .filter-icon {
    width: 2.9vw;
    height: 2.9vw;
    margin-right: 1.04vw;
  }
}
</style>
