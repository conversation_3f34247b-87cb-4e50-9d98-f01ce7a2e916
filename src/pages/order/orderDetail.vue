<template>
  <view class="order-detail">
    <uni-nav-bar
      title="工单详情"
      left-icon="left"
      :border="false"
      status-bar
      fixed
      background-color="#2979ff"
      color="#fff"
      @clickLeft="goBack"
    />
    <view class="content">
      <!-- 顶部信息 -->
      <view class="top-info">
        <view
          class="info-item"
          v-for="item in topInfo"
          @tap="openPopup(item.id)"
          :key="item.id"
        >
          <view class="icon" :style="{ backgroundColor: item.color }">
            <image :src="item.icon" mode="aspectFit" />
          </view>
          <view class="text">{{ item.text }}</view>
        </view>
      </view>
      <view class="status-list">
        <view class="status-item">
          <view class="status-item-line">
            接车
            <uni-icons
              color="var(--primary-color)"
              type="checkbox-filled"
              size="16"
            ></uni-icons>
          </view>
          <view class="status-item-content">
            <view class="status-item-content-card">
              <view class="content-card-main">
                <view class="main-row">
                  订单编号：{{ orderDetail.ordercode }}
                </view>
                <view class="main-row">
                  开单时间：{{ orderDetail.create_time }}
                </view>
                <view class="main-row">
                  接车标注：{{ orderDetail.remark }}
                </view>
                <view class="main-row">
                  车主嘱托：{{ orderDetail.pickup_note }}
                </view>
              </view>
              <view class="content-card-btn" @tap="toDetail">详情</view>
            </view>
          </view>
        </view>
        <view class="status-item">
          <view class="status-item-line">
            服务
            <uni-icons
              color="#BCBCBC"
              type="checkbox-filled"
              size="16"
            ></uni-icons>
          </view>
          <view class="status-item-content">
            <view class="status-item-content-card">
              <view class="content-card-main">
                <view
                  class="main-row"
                  v-for="service in orderDetail.car_order_service"
                  :key="service.id"
                >
                  <view class="row-item">{{ service.title }}</view>
                  <view class="row-item">小计：{{ service.price }}元</view>
                </view>
              </view>
              <view class="content-card-btn" @tap="openPopup('service')">
                管理
              </view>
            </view>
            <view class="total-view">
              <view class="total-view-item">
                小计：<text>{{ orderDetail.car_order_service?.length }}</text
                >项
              </view>
              <view class="total-view-item">
                金额小计：<text>{{ service_price }}</text
                >元
              </view>
            </view>
          </view>
        </view>
        <view class="status-item">
          <view class="status-item-line">
            配件
            <uni-icons
              color="#BCBCBC"
              type="checkbox-filled"
              size="16"
            ></uni-icons>
          </view>
          <view class="status-item-content">
            <view class="status-item-content-card">
              <view class="content-card-main">
                <view
                  class="main-row"
                  v-for="accessory in orderDetail.car_order_product"
                  :key="accessory.id"
                >
                  <view class="row-item">{{ accessory.title }}</view>
                  <view class="row-item">
                    {{ accessory.num }}
                    <text>件</text>
                  </view>
                  <view class="row-item">小计：{{ accessory.amount }}元</view>
                </view>
              </view>
              <view class="content-card-btn" @tap="openPopup('accessory')"
                >管理</view
              >
            </view>
            <view class="total-view">
              <view class="total-view-item">
                小计：<text>{{ orderDetail.car_order_product?.length }}</text
                >项
              </view>
              <view class="total-view-item">
                金额小计：<text>{{ accessory_price }}</text
                >元
              </view>
            </view>
          </view>
        </view>
        <view class="status-item">
          <view class="status-item-line">
            施工
            <uni-icons
              color="#BCBCBC"
              type="checkbox-filled"
              size="16"
            ></uni-icons>
          </view>
          <view class="status-item-content">
            <view class="status-item-content-card">
              <view class="content-card-main">
                <view
                  class="main-row"
                  :key="index"
                  v-for="(item, index) in orderDetail.service_work"
                >
                  <view class="row-item">{{ item.user_name }}</view>
                  <view class="row-item">
                    <text>{{ item.work_num }}</text
                    >项工作
                  </view>
                  <view class="row-item"
                    >工时<text>{{ item.work_hours }}</text
                    >个</view
                  >
                </view>
              </view>
              <view
                @tap="openPopup('dispatch')"
                class="content-card-btn"
                >派工</view
              >
            </view>
            <view class="btns">
              <view class="order-btn" @tap="openPopup('sendWork')">施工</view>
              <view class="order-btn" @tap="openPopup('quality')">质检</view>
              <view class="order-btn" @tap="openPopup('repair')">返修</view>
            </view>
            <view class="photo-list">
              <view
                class="photo-item"
                v-for="(item, index) in orderDetail.service_maintenance_image"
                :key="'photo' + index"
              >
                <view class="photo-title"
                  >{{ item.user_name
                  }}{{ item.maintenance_time }} 施工拍照</view
                >
                <view class="photo-img">
                  <view class="photo-img-item">
                    <image
                      class="photo-img-item-img"
                      src="/static/icons/photo.png"
                      mode="aspectFit"
                    />
                    <text class="photo-img-item-text">维修前</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="status-item">
          <view class="status-item-line">
            结账
            <uni-icons
              color="#BCBCBC"
              type="checkbox-filled"
              size="16"
            ></uni-icons>
          </view>
          <view class="status-item-content">
            <view class="status-item-content-card">
              <view class="content-card-main">
                <view class="main-row row-text-size">
                  <view class="row-item">
                    <text>合计应付：</text>
                    <text>{{ orderDetail.order_money }}</text>
                  </view>
                  <view class="row-item">
                    <text>会员折扣：</text>
                    <text>{{
                      orderDetail.member_discount
                        ? orderDetail.member_discount
                        : 0
                    }}</text>
                  </view>
                </view>
                <view class="main-row row-text-size">
                  <view class="row-item">
                    <text>套餐使用：</text>
                    <text class="size26">{{
                      orderDetail.member_discount
                    }}</text>
                  </view>
                  <view class="row-item">
                    <text>套餐剩余：</text>
                    <text class="size26">1000</text>
                  </view>
                </view>
                <view class="main-row row-text-size">
                  <view class="row-item">
                    <text>实际应付：</text>
                    <text class="size26">{{
                      orderDetail.real_receivable_price
                    }}</text>
                  </view>
                  <view class="row-item">
                    <text>支付方式：</text>
                    <text>微信支付</text>
                  </view>
                </view>
                <view class="main-row row-text-size">
                  <view class="row-item">
                    <text>挂账状态：</text>
                    <text>{{
                      orderDetail.is_on_credit ? "未挂账" : "挂账"
                    }}</text>
                  </view>
                  <view class="row-item">
                    <text>挂账金额：</text>
                    <text class="size26">1000</text>
                  </view>
                </view>
              </view>
              <view class="content-card-btn" @tap="openPopup('settlement')"
                >结算</view
              >
            </view>
          </view>
        </view>
        <view class="status-item">
          <view class="status-item-line pick-car">
            提车
            <uni-icons
              color="#BCBCBC"
              type="checkbox-filled"
              size="16"
            ></uni-icons>
          </view>
          <view class="status-item-row">
            <text>已提车</text>
            <view class="order-btn" @tap="getCar">提车</view>
          </view>
        </view>
        <view class="status-item">
          <view class="status-item-line">
            营销
            <uni-icons
              color="#BCBCBC"
              type="checkbox-filled"
              size="16"
            ></uni-icons>
          </view>
          <view class="status-item-content">
            <view class="btns-view">
              <view class="order-btn"> 用车建议 </view>
              <view class="order-btn">创建计划</view>
            </view>
            <view class="marketing">
              <view
                class="marketing-item"
                v-for="(note, key) in orderDetail.store_note"
                :key="key"
              >
                <view class="marketing-item-time">{{ note.add_time }}</view>
                <view class="marketing-item-desc">
                  用车建议：{{ note.content }}
                </view>
              </view>
              <view class="marketing-item">
                <view class="marketing-item-time">2024-01-01 12:00:00</view>
                <view class="marketing-item-desc">
                  预约计划：{{ "" }}
                  <text class="look">查看</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <uni-popup ref="popup" :mask-click="false" type="bottom">
      <view class="popup-content">
        <view class="popup-content-title">
          <text>{{ DetailText[popupId] }}</text>
          <uni-icons
            @tap="popup.close()"
            size="24"
            type="close"
            color="#999"
            class="close-icon"
          />
        </view>
        <top-info
          v-if="
            popupId === 'customerInfo' ||
            popupId === 'carInfo' ||
            popupId === 'history'
          "
          :top-type="popupId"
          :info="orderDetail.customer_info"
          :car_info="orderDetail.car_info"
          :car_id="orderDetail.car_id"
        />
        <settlement v-else-if="popupId === 'settlement'" />
        <send-work v-else :currentType="popupId" />
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { importFile } from "@/utils/file";
import { ref, computed } from "vue";
import TopInfo from "./components/topInfo.vue";
import Settlement from "./components/settlement.vue";
import SendWork from "./components/sendWork.vue";
import { getOrderDetail } from "@/api/order";
import { onLoad } from "@dcloudio/uni-app";
import type { TableData } from "@/utils/type";

enum DetailText {
  customerInfo = "客户档案",
  carInfo = "车辆信息",
  history = "进店历史",
  service = "服务管理",
  accessory = "配件管理",
  sendWork = "施工管理",
  quality = "完工自检",
  settlement = "订单结算",
}

const popup = ref();
const popupId = ref<keyof typeof DetailText>();
const orderDetail = ref<TableData>({});
const topInfo = ref([
  {
    icon: importFile("/static/images/order/order_detail01.png"),
    text: "客户档案",
    color: "#616161",
    id: "customerInfo",
  },
  {
    icon: importFile("/static/images/order/order_detail02.png"),
    text: "车辆信息",
    color: "#45501B",
    id: "carInfo",
  },
  {
    icon: importFile("/static/images/order/order_detail03.png"),
    text: "进店历史",
    color: "#235956",
    id: "history",
  },
]);
const openPopup = (id: string) => {
  popupId.value = id as keyof typeof DetailText;
  console.log(popupId.value);
  popup.value.open();
};
const goBack = () => {
  uni.navigateBack();
};
const toDetail = () => {};
const getCar = () => {};
const service_price = computed(() => {
  return orderDetail.value.car_order_service.reduce(
    (acc: number, curr: any) => {
      return acc + Number(curr.price);
    },
    0
  );
});
const accessory_price = computed(() => {
  return orderDetail.value.car_order_product.reduce(
    (acc: number, curr: any) => {
      return acc + Number(curr.price);
    },
    0
  );
});

onLoad((options) => {
  if (options?.id) {
    getOrderDetail({ order_id: options.id }).then((res: any) => {
      if (res.code === 200) {
        orderDetail.value = res.data;
      }
    });
  }
});
</script>

<style lang="scss" scoped>
.order-detail {
  min-height: 100vh;
  background-color: #f5f5f5;

  .content {
    padding: 0;
    color: #181818;

    .top-info {
      display: flex;
      justify-content: space-between;
      padding: 30rpx 20rpx;
      background-color: #fff;
      margin-bottom: 20rpx;

      .info-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;

        .icon {
          width: 68rpx;
          height: 68rpx;
          margin-bottom: 12rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;

          image {
            width: 32rpx;
            height: 32rpx;
          }
        }

        .text {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 12rpx;
        }

        .detail-btn {
          padding: 4rpx 20rpx;
          background-color: var(--primary-color);
          border-radius: 30rpx;
          color: #fff;
          font-size: 24rpx;
        }
      }
    }
    .status-list {
      .status-item {
        display: flex;
        padding: 0 20rpx;
        &-line {
          padding-right: 20rpx;
          margin-right: 28rpx;
          flex-shrink: 0;
          font-size: 28rpx;
          border-right: 1rpx solid #e5e5e5;
          position: relative;
          line-height: 1;
          box-sizing: border-box;

          &.pick-car {
            padding-top: 10rpx;
            .uni-icons {
              top: 10rpx;
            }
          }
          .uni-icons {
            position: absolute;
            right: -8px;
            top: 0;
          }
        }
        &-content {
          flex: 1;
          padding-bottom: 20rpx;
          &-card {
            background-color: #fff;
            border-radius: 12rpx;
            display: flex;
            .content-card-main {
              width: calc(100% - 56rpx);
              padding: 20rpx;
              .main-row {
                line-height: 1.8;
                font-size: 26rpx;
                display: flex;
                justify-content: space-between;
                &.row-text-size {
                  font-size: 22rpx;
                }
              }
            }
            .content-card-btn {
              width: 56rpx;
              height: auto;
              flex-shrink: 0;
              color: #fff;
              background-color: var(--primary-color);
              border-radius: 0 10rpx 10rpx 0;
              font-size: 30rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              text-align: center;
            }
          }
          .total-view {
            display: flex;
            justify-content: space-between;
            padding: 30rpx 20rpx 10rpx 20rpx;
            .total-view-item {
              font-size: 26rpx;
            }
          }
          .btns {
            display: flex;
            justify-content: space-between;
            padding: 30rpx 0;
          }
          .photo-list {
            .photo-item {
              .photo-title {
                font-size: 20rpx;
                padding: 10rpx 0;
              }
              .photo-img {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 10rpx;
                &-item {
                  width: 144rpx;
                  font-size: 20rpx;
                  text-align: center;
                  &-img {
                    width: 100%;
                    height: 112rpx;
                    border-radius: 10rpx;
                  }
                  &-text {
                    padding: 10rpx 0 30rpx 0;
                  }
                }
              }
            }
          }
          .btns-view {
            display: flex;
            justify-content: flex-end;
            gap: 20rpx;
            padding-bottom: 20rpx;
          }
          .marketing {
            line-height: 1.8;
            .marketing-item {
              &-time {
                font-size: 20rpx;
              }
              .look {
                margin-left: 16rpx;
                color: var(--primary-color);
              }
            }
          }
        }
        .status-item-row {
          width: 100%;
          font-size: 26rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 4rpx 0 40rpx;
          line-height: 1;
        }
      }
    }
  }
  .size26 {
    font-size: 26rpx;
    color: var(--primary-color);
  }
  .order-btn {
    display: inline-block;
    padding: 12rpx 43rpx;
    background-color: var(--primary-color);
    border-radius: 10rpx;
    font-size: 24rpx;
    letter-spacing: 6rpx;
    color: #fff;
  }
  .popup-content {
    border-radius: 16rpx 16rpx 0 0;
    background-color: #fff;

    &-title {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20rpx;
      position: relative;
      font-size: 36rpx;
      font-weight: bold;
      color: #515151;
      .close-icon {
        position: absolute;
        right: 20rpx;
        top: 20rpx;
        font-weight: normal;
      }
    }
  }
}
</style>
