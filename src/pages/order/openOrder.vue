<template>
  <view class="vehicle-page">
    <uni-nav-bar
      title="维修开单"
      left-icon="left"
      :border="false"
      status-bar
      fixed
      background-color="var(--primary-color)"
      color="#fff"
      @clickLeft="goBack"
    />

    <view class="form-container">
      <!-- 基本信息表单 -->
      <view class="form-section">
        <view class="form-item">
          <text class="label">车牌号</text>
          <input
            class="input"
            type="text"
            placeholder="请输入车牌号"
            placeholder-class="placeholder"
            v-model="formData.car_license_code"
            @focus="keyboard.open"
          />
          <view class="scan-btn" @tap="openCamera('car_license_code')">
            <image
              class="vin-icon"
              src="/static/images/warehouse/vip.png"
              mode="aspectFit"
            ></image>
            <uv-icon name="scan" size="24" color="#333"></uv-icon>
          </view>
        </view>

        <view class="form-item">
          <text class="label">VIN</text>
          <input
            class="input"
            type="text"
            placeholder="请输入VIN"
            placeholder-class="placeholder"
            v-model="formData.car_vin_code"
          />
          <view class="scan-btn" @tap="openCamera('car_vin_code')">
            <image
              class="vin-icon"
              src="/static/images/warehouse/vip.png"
              mode="aspectFit"
            ></image>
            <uv-icon name="scan" size="24" color="#333"></uv-icon>
          </view>
        </view>

        <view class="form-item arrow">
          <text class="label">车型品牌</text>
          <text class="value"></text>
          <uni-icons type="right" size="16" color="#666"></uni-icons>
        </view>

        <view class="form-item">
          <text class="label">客户姓名</text>
          <input
            class="input"
            type="text"
            placeholder="请输入客户姓名"
            placeholder-class="placeholder"
            v-model="formData.send_repair_fullname"
          />
        </view>

        <view class="form-item">
          <text class="label">送修电话</text>
          <input
            class="input"
            type="number"
            placeholder="请输入送修电话"
            placeholder-class="placeholder"
            v-model="formData.send_repair_phone"
          />
        </view>

        <view class="form-item">
          <text class="label">当前里程</text>
          <input
            class="input"
            type="number"
            placeholder="请输入当前里程"
            placeholder-class="placeholder"
            v-model="formData.cur_mileage"
          />
        </view>

        <view class="form-item">
          <text class="label">接车标注</text>
          <input
            class="input"
            type="text"
            placeholder="请输入接车标注"
            placeholder-class="placeholder"
            v-model="formData.pickup_note"
          />
        </view>
      </view>

      <!-- 照片上传区域 -->
      <view class="upload-section">
        <view class="upload-grid">
          <view
            class="upload-item"
            v-for="(item, index) in uploadItems"
            :key="index"
          >
            <view class="item-cover" @tap="uploadCarPic(index)">
              <uv-icon
                class="camera"
                name="camera"
                size="16"
                color="#333"
              ></uv-icon>
              <image class="upload-icon" :src="item.icon" mode="aspectFit" />
            </view>
            <text class="upload-text">{{ item.text }}</text>
          </view>
        </view>
      </view>

      <!-- 备注区域 -->
      <view class="note-section">
        <textarea
          class="note-textarea"
          placeholder="备注内容"
          placeholder-class="placeholder"
          v-model="formData.pickup_note"
        />
      </view>

      <!-- 提示信息 -->
      <view class="warning-section">
        <text class="warning-text">上传汽车健康电子档案资料请补充以下资料</text>
      </view>

      <!-- 附加信息表单 -->
      <view class="form-section">
        <view class="form-item">
          <text class="label">车辆所有人</text>
          <input
            class="input"
            type="text"
            placeholder="请输入车辆所有人"
            placeholder-class="placeholder"
            v-model="formData.vehicleowner"
          />
        </view>

        <view class="form-item">
          <text class="label">发动机号</text>
          <input
            class="input"
            type="text"
            placeholder="请输入发动机号"
            placeholder-class="placeholder"
            v-model="formData.enginecode"
          />
        </view>
      </view>

      <!-- 提交按钮 -->
      <button class="submit-btn" @click="handleSubmit" type="primary">
        确定
      </button>
    </view>
    <uv-keyboard
      ref="keyboard"
      mode="car"
      :disKeys="['O', 'I']"
      :tips="carLicenseCode"
      @change="change"
      @confirm="confirmKeyboard"
      @backspace="backspace"
    ></uv-keyboard>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { createOrder, ocrVin, ocrCarCard } from "@/api/order";
import { onLoad } from "@dcloudio/uni-app";
import { uploadFile } from "@/utils/file";
import type { Response } from "@/utils/type";

interface FormData {
  thetype: number; // 开单类型 1快修保养 2洗车美容 3换件维修
  car_id: number; // 车辆id 有用户时有值 新用户为0
  car_license_code: string; // 车牌号
  car_vin_code: string; // 车架号
  car_brand: number; // 车型品牌ID
  send_repair_fullname: string; // 客户姓名
  send_repair_phone: string; // 送修电话
  cur_mileage: string; // 当前里程 KM
  pickup_pics: string; // 接车标注，多个图片使用英文竖线分割  车身正面|车身左侧|车身右侧|车身尾部|油表拍照|胎压标注|其它照片1|其它照片2|其它照片3
  pickup_note: string; // 备注内容
  vehicleowner: string; // 车辆所有人
  enginecode: string; // 发动机号
  [key: string]: string | number;
}
const formData = ref<FormData>({
  thetype: 1,
  car_id: 0,
  car_license_code: "",
  car_vin_code: "",
  car_brand: 0,
  send_repair_fullname: "",
  send_repair_phone: "",
  cur_mileage: "",
  pickup_pics: "",
  pickup_note: "",
  vehicleowner: "",
  enginecode: "",
});
const keyboard = ref();
const uploadItems = ref([
  { icon: "", text: "车身正面" },
  { icon: "", text: "车身左侧" },
  { icon: "", text: "车身右侧" },
  { icon: "", text: "车身尾部" },
  { icon: "", text: "油表拍照" },
  { icon: "", text: "胎压标注" },
  { icon: "", text: "其他照片" },
  { icon: "", text: "其他照片" },
  { icon: "", text: "其他照片" },
]);

const goBack = () => {
  uni.navigateBack();
};
onLoad((options: any) => {
  formData.value.thetype = Number(options.type) || 1;
});
const carLicenseCode = ref("");
const change = (value: string) => {
  carLicenseCode.value += value;
  if (carLicenseCode.value.length > 7) {
    carLicenseCode.value = carLicenseCode.value.slice(0, 7);
  } else if (carLicenseCode.value.length === 1) {
    keyboard.value.changeCarMode();
  }
};
const confirmKeyboard = (value: string) => {
  formData.value.car_license_code = carLicenseCode.value;
};
const backspace = () => {
  carLicenseCode.value = carLicenseCode.value.slice(0, -1);
  if (carLicenseCode.value.length === 0) {
    keyboard.value.changeCarMode();
  }
};
const is_vip = ref(true);
const openCamera = async (type: string) => {
  if (is_vip.value) {
    let fn = type === "car_license_code" ? ocrCarCard : ocrVin;
    const res: Response = JSON.parse((await uploadFile(1)) as string);
    if (res.code === 200) {
      const res1: Response = (await fn(formData.value)) as Response;
      if (res1.code === 200) {
        formData.value[type] = res1.data.paths[0];
      }
    }
  } else {
    uni.showToast({
      title: "请先开通VIP",
      icon: "none",
    });
  }
};
// 上传车照片
const uploadCarPic = async (index: number) => {
  const res: Response = (await uploadFile(1)) as Response;
  if (res.code === 200) {
    uploadItems.value[index].icon = res.data.paths[0];
  } else {
    uni.showToast({
      title: "上传失败",
      icon: "none",
    });
  }
};
const handleSubmit = async () => {
  if (formData.value.car_license_code === "") {
    uni.showToast({
      title: "请输入车牌号",
      icon: "none",
    });
    return;
  }
  if (formData.value.car_vin_code === "") {
    uni.showToast({
      title: "请输入车架号",
      icon: "none",
    });
    return;
  }
  if (formData.value.pickup_pics === "") {
    uni.showToast({
      title: "请上传照片",
      icon: "none",
    });
    return;
  }
  formData.value.pickup_pics = uploadItems.value
    .map((item) => item.icon)
    .join("|");
  const res: Response = (await createOrder(formData.value)) as Response;
  if (res.code === 200) {
    uni.showToast({
      title: "开单成功",
      icon: "success",
    });
  }
};
</script>

<style lang="scss" scoped>
.vehicle-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  // padding-top: 88rpx;

  .form-container {
    padding: 20rpx;
  }

  // 表单部分样式
  .form-section {
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 20rpx;

    .form-item {
      display: flex;
      align-items: center;
      padding: 24rpx 20rpx;
      border-bottom: 1rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      &.arrow {
        justify-content: space-between;
      }

      .label {
        width: 160rpx;
        font-size: 28rpx;
        color: #333;
      }

      .input {
        flex: 1;
        font-size: 28rpx;
        padding: 0 20rpx;
      }

      .scan-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
      }

      .vin-icon {
        width: 38rpx;
        height: 20rpx;
        position: absolute;
        top: -10rpx;
        right: -20rpx;
      }

      .value {
        flex: 1;
        font-size: 28rpx;
        color: #999;
      }
    }
  }

  // 照片上传区域样式
  .upload-section {
    margin-bottom: 20rpx;

    .upload-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20rpx;

      .upload-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .item-cover {
          width: 180rpx;
          height: 180rpx;
          background-color: #fff;
          border-radius: 12rpx;
          border: 1rpx solid #f5f5f5;
          box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
          position: relative;
          .camera {
            position: absolute;
            top: 10rpx;
            right: 12rpx;
          }
          .upload-icon {
            width: 100%;
            height: 100%;
          }
        }

        .upload-text {
          font-size: 24rpx;
          margin-top: 20rpx;
          color: #666;
        }
      }
    }
  }

  // 备注区域样式
  .note-section {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;

    .note-textarea {
      width: 100%;
      height: 200rpx;
      font-size: 28rpx;
    }
  }

  // 警告提示样式
  .warning-section {
    margin-bottom: 20rpx;

    .warning-text {
      font-size: 24rpx;
      color: #ff6b00;
    }
  }

  // 提交按钮样式
  .submit-btn {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    background-color: #2979ff;
    color: #fff;
    font-size: 32rpx;
    border-radius: 44rpx;
    margin-top: 40rpx;

    &:active {
      opacity: 0.8;
    }
  }
}

// 占位符样式
.placeholder {
  color: #999;
  font-size: 28rpx;
}
</style>
