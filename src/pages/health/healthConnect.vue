<template>
  <view class="health-connect">
    <uni-nav-bar
      title="健康档案对接"
      left-icon="left"
      @clickLeft="handleBack"
      :border="false"
      :dark="true"
      status-bar
      fixed
      background-color="#2979ff"
      color="#fff"
    />
    
    <view class="header">
      <image class="logo" src="/static/images/ministry-logo.png" mode="aspectFit" />
      <text class="title">中华人民共和国交通运输部</text>
      <text class="sub-title">Ministry of Transport of the People's Republic of China</text>
    </view>
    
    <view class="content">
      <view class="notice">
        根据交通运输部关于汽车电子健康档案系统工作要求，车店助手已严格按照：四川省、安徽省、重庆市、福建省、甘肃省、广东省、广西、贵州省、河北省、黑龙江省、河南省、湖南省、宁夏、山东省、天津市、浙江省等地区对标准，完成汽车电子健康档案系统对接。
      </view>
      
      <view class="status-box">
        <text class="label">当前位置：</text>
        <text class="value">湖南省</text>
        <text class="status success">已开通</text>
      </view>
      
      <view class="info-box">
        <view class="info-item">
          <text class="label">企业名称：</text>
          <text class="value">明鑫汽车维修服务部</text>
          <uni-icons type="compose" size="16" color="#666" />
        </view>
        
        <view class="info-item">
          <text class="label">开通时长：</text>
          <text class="value">1</text>
          <text class="unit">年</text>
          <text class="price">服务费：</text>
          <text class="amount">168.00</text>
          <text class="unit">元</text>
        </view>
      </view>
    </view>
    
    <view class="footer">
      <button class="submit-btn" @click="handleSubmit">确定开通</button>
    </view>
  </view>
</template>

<script lang="ts" setup>
const handleBack = () => {
  uni.navigateBack();
};

const handleSubmit = () => {
  uni.navigateTo({
    url: '/pages/health/healthForm'
  });
};
</script>

<style lang="scss" scoped>
.health-connect {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 88rpx;
  
  .header {
    background-color: #fff;
    padding: 40rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .logo {
      width: 120rpx;
      height: 120rpx;
      margin-bottom: 20rpx;
    }
    
    .title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
    }
    
    .sub-title {
      font-size: 24rpx;
      color: #666;
    }
  }
  
  .content {
    margin-top: 20rpx;
    background-color: #fff;
    padding: 30rpx;
    
    .notice {
      font-size: 28rpx;
      color: #333;
      line-height: 1.6;
      margin-bottom: 30rpx;
    }
    
    .status-box {
      display: flex;
      align-items: center;
      margin-bottom: 30rpx;
      
      .label {
        font-size: 28rpx;
        color: #333;
      }
      
      .value {
        font-size: 28rpx;
        color: #333;
        margin-right: 20rpx;
      }
      
      .status {
        font-size: 24rpx;
        padding: 4rpx 12rpx;
        border-radius: 4rpx;
        
        &.success {
          background-color: #e8f5e9;
          color: #4caf50;
        }
      }
    }
    
    .info-box {
      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        
        .label {
          font-size: 28rpx;
          color: #333;
        }
        
        .value {
          font-size: 28rpx;
          color: #333;
          margin-right: 10rpx;
        }
        
        .unit {
          font-size: 28rpx;
          color: #666;
          margin-right: 20rpx;
        }
        
        .price {
          font-size: 28rpx;
          color: #333;
        }
        
        .amount {
          font-size: 28rpx;
          color: #ff6b35;
          margin: 0 10rpx;
        }
      }
    }
  }
  
  .footer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 20rpx 30rpx;
    background-color: #fff;
    
    .submit-btn {
      width: 100%;
      height: 88rpx;
      line-height: 88rpx;
      background-color: #2979ff;
      color: #fff;
      font-size: 32rpx;
      border-radius: 4rpx;
    }
  }
}
</style> 