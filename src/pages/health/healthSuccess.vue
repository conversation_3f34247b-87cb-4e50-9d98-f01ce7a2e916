<template>
  <view class="health-success">
    <uni-nav-bar
      title="开通后"
      left-icon="left"
      @clickLeft="handleBack"
      :border="false"
      :dark="true"
      status-bar
      fixed
      background-color="#2979ff"
      color="#fff"
    />
    
    <view class="content">
      <view class="info-box">
        <view class="info-item">
          <text class="label">企业名称：</text>
          <text class="value">钢铁三合汽车维修服务有限公司</text>
        </view>
        
        <view class="info-item">
          <text class="label">企业编码：</text>
          <text class="value">410502606369826</text>
        </view>
        
        <view class="info-item">
          <text class="label">配户密码：</text>
          <text class="value">124578AA@q</text>
        </view>
        
        <view class="info-item">
          <text class="label">服务期限：</text>
          <text class="value">2025.06.19 ~ 2026.06.18</text>
        </view>
      </view>
      
      <view class="calendar">
        <view class="calendar-header">
          <text class="month">2025/04</text>
          <view class="nav-buttons">
            <text class="nav-btn">上一月</text>
            <text class="nav-btn">下一月</text>
          </view>
        </view>
        
        <view class="calendar-body">
          <view class="weekdays">
            <text v-for="day in ['日', '一', '二', '三', '四', '五', '六']" :key="day" class="weekday">{{day}}</text>
          </view>
          
          <view class="days">
            <!-- 日期格子，这里简化展示 -->
            <view v-for="i in 30" :key="i" class="day-cell" :class="{'current': i === 19}">{{i}}</view>
          </view>
        </view>
      </view>
      
      <view class="record-list">
        <view class="record-item" v-for="i in 4" :key="i">
          <view class="time">13:23</view>
          <view class="car-info">鲁AA78990</view>
          <view class="mileage">345678</view>
          <view class="service-type">休保养</view>
          <view class="actions">
            <text class="action-btn">查</text>
            <text class="action-btn">看</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
const handleBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.health-success {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 88rpx;
  
  .content {
    padding: 20rpx;
    
    .info-box {
      background-color: #fff;
      padding: 20rpx 30rpx;
      border-radius: 8rpx;
      margin-bottom: 20rpx;
      
      .info-item {
        display: flex;
        margin-bottom: 20rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .label {
          width: 140rpx;
          font-size: 28rpx;
          color: #333;
        }
        
        .value {
          flex: 1;
          font-size: 28rpx;
          color: #333;
        }
      }
    }
    
    .calendar {
      background-color: #fff;
      padding: 20rpx;
      border-radius: 8rpx;
      margin-bottom: 20rpx;
      
      .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;
        
        .month {
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
        }
        
        .nav-buttons {
          display: flex;
          gap: 20rpx;
          
          .nav-btn {
            font-size: 24rpx;
            color: #666;
          }
        }
      }
      
      .calendar-body {
        .weekdays {
          display: flex;
          margin-bottom: 10rpx;
          
          .weekday {
            flex: 1;
            text-align: center;
            font-size: 24rpx;
            color: #666;
          }
        }
        
        .days {
          display: flex;
          flex-wrap: wrap;
          
          .day-cell {
            width: calc(100% / 7);
            height: 60rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24rpx;
            color: #333;
            
            &.current {
              background-color: #2979ff;
              color: #fff;
            }
          }
        }
      }
    }
    
    .record-list {
      background-color: #fff;
      border-radius: 8rpx;
      
      .record-item {
        display: flex;
        align-items: center;
        padding: 20rpx 30rpx;
        border-bottom: 1px solid #eee;
        
        &:last-child {
          border-bottom: none;
        }
        
        .time {
          width: 100rpx;
          font-size: 24rpx;
          color: #666;
        }
        
        .car-info {
          width: 160rpx;
          font-size: 28rpx;
          color: #333;
        }
        
        .mileage {
          width: 120rpx;
          font-size: 28rpx;
          color: #333;
        }
        
        .service-type {
          flex: 1;
          font-size: 28rpx;
          color: #333;
        }
        
        .actions {
          display: flex;
          gap: 20rpx;
          
          .action-btn {
            width: 60rpx;
            height: 44rpx;
            line-height: 44rpx;
            text-align: center;
            background-color: #f5f5f5;
            color: #2979ff;
            font-size: 24rpx;
            border-radius: 4rpx;
          }
        }
      }
    }
  }
}
</style> 