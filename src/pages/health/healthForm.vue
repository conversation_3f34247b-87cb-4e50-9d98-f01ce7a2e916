<template>
  <view class="health-form">
    <uni-nav-bar
      title="健康档案对接"
      left-icon="left"
      @clickLeft="handleBack"
      :border="false"
      :dark="true"
      status-bar
      fixed
      background-color="#2979ff"
      color="#fff"
    />
    
    <view class="form-content">
      <view class="form-item">
        <text class="label">企业名称：</text>
        <input class="input" v-model="formData.companyName" placeholder="请输入企业名称" />
      </view>
      
      <view class="form-item">
        <text class="label">信用代码：</text>
        <input class="input" v-model="formData.creditCode" placeholder="请输入统一社会信用代码" />
      </view>
      
      <view class="form-item">
        <text class="label">所在地市：</text>
        <view class="input-right" @click="showCityPicker">
          <text :class="{'placeholder': !formData.city}">{{formData.city || '请选择所在地市'}}</text>
          <uni-icons type="right" size="16" color="#666" />
        </view>
      </view>
      
      <view class="form-item">
        <text class="label">详细地址：</text>
        <input class="input" v-model="formData.address" placeholder="请输入详细地址" />
      </view>
      
      <view class="form-item">
        <text class="label">负责人：</text>
        <input class="input" v-model="formData.manager" placeholder="请输入企业负责人姓名" />
      </view>
      
      <view class="form-item">
        <text class="label">联系电话：</text>
        <input class="input" v-model="formData.phone" placeholder="请输入联系电话" />
      </view>
      
      <view class="form-item">
        <text class="label">备案号：</text>
        <input class="input" v-model="formData.recordNo" placeholder="请输入道路运输经营备案证明号或许可证号" />
      </view>
      
      <view class="form-item">
        <text class="label">企业编码：</text>
        <input class="input" v-model="formData.companyCode" placeholder="请输入企业编码" />
      </view>
      
      <view class="form-item">
        <text class="label">资产代码：</text>
        <input class="input" v-model="formData.assetCode" placeholder="请输入资产代码" />
      </view>
    </view>
    
    <view class="footer">
      <button class="next-btn" @click="handleNext">下一步</button>
    </view>
    
    <!-- 城市选择器 -->
    <uni-popup ref="cityPopup" type="bottom">
      <uni-list>
        <uni-list-item title="长沙市" @click="selectCity('长沙市')" />
        <uni-list-item title="株洲市" @click="selectCity('株洲市')" />
        <uni-list-item title="湘潭市" @click="selectCity('湘潭市')" />
        <uni-list-item title="衡阳市" @click="selectCity('衡阳市')" />
      </uni-list>
    </uni-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const cityPopup = ref();

const formData = ref({
  companyName: '',
  creditCode: '',
  city: '',
  address: '',
  manager: '',
  phone: '',
  recordNo: '',
  companyCode: '',
  assetCode: ''
});

const handleBack = () => {
  uni.navigateBack();
};

const showCityPicker = () => {
  cityPopup.value.open();
};

const selectCity = (city: string) => {
  formData.value.city = city;
  cityPopup.value.close();
};

const handleNext = () => {
  // 这里可以添加表单验证
  uni.navigateTo({
    url: '/pages/health/healthSuccess'
  });
};
</script>

<style lang="scss" scoped>
.health-form {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-top: 88rpx;
  padding-bottom: 140rpx;
  
  .form-content {
    margin-top: 20rpx;
    background-color: #fff;
    
    .form-item {
      display: flex;
      align-items: center;
      padding: 20rpx 30rpx;
      border-bottom: 1px solid #eee;
      
      .label {
        width: 160rpx;
        font-size: 28rpx;
        color: #333;
      }
      
      .input {
        flex: 1;
        height: 44rpx;
        font-size: 28rpx;
      }
      
      .input-right {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        
        text {
          font-size: 28rpx;
          color: #333;
          
          &.placeholder {
            color: #999;
          }
        }
      }
    }
  }
  
  .footer {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 20rpx 30rpx;
    background-color: #fff;
    
    .next-btn {
      width: 100%;
      height: 88rpx;
      line-height: 88rpx;
      background-color: #2979ff;
      color: #fff;
      font-size: 32rpx;
      border-radius: 4rpx;
    }
  }
}
</style> 