<template>
  <view class="health-activate">
    <view class="content">
      <view class="title">汽车电子健康档案系统</view>
      <view class="sub-title">由交通部相关部门提供</view>
      
      <view class="desc">
        为了贯彻执行《国家信息化发展战略纲要》及《交通运输部办公厅关于开展汽车电子健康档案系统试点工作的通知》等号，车店助手按照《机动车维修管理规定》，专项组织技术力量及服务团队为您打开了全国统一对接服务。
      </view>
      
      <view class="notice">
        由于对接服务需要大量的人力对接成本和技术支撑，本软件本费用不予减免优惠，请根据当地相关部门对接使用用户要求，一旦开通，我们将立即按照交通运输部相关要求，为您提交文档资料并审核。
      </view>
      
      <view class="button-group">
        <button class="cancel-btn" @click="handleCancel">暂不开通</button>
        <button class="confirm-btn" @click="handleConfirm">确定开通</button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
const handleCancel = () => {
  uni.navigateBack();
};

const handleConfirm = () => {
  uni.navigateTo({
    url: '/pages/health/healthForm'
  });
};
</script>

<style lang="scss" scoped>
.health-activate {
  min-height: 100vh;
  background-color: #fff;
  padding: 40rpx;
  
  .content {
    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      text-align: center;
      margin-bottom: 20rpx;
    }
    
    .sub-title {
      font-size: 28rpx;
      color: #666;
      text-align: center;
      margin-bottom: 60rpx;
    }
    
    .desc {
      font-size: 28rpx;
      color: #333;
      line-height: 1.6;
      margin-bottom: 40rpx;
    }
    
    .notice {
      font-size: 28rpx;
      color: #333;
      line-height: 1.6;
      margin-bottom: 60rpx;
    }
    
    .button-group {
      display: flex;
      gap: 20rpx;
      
      button {
        flex: 1;
        height: 88rpx;
        line-height: 88rpx;
        font-size: 32rpx;
        border-radius: 4rpx;
      }
      
      .cancel-btn {
        background-color: #f5f5f5;
        color: #666;
      }
      
      .confirm-btn {
        background-color: #2979ff;
        color: #fff;
      }
    }
  }
}
</style> 