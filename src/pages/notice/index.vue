<template>
  <view class="notice-index">
    <uni-nav-bar
      :border="false"
      title="消息/聊天"
      color="#fff"
      backgroundColor="#1976d2"
    />
    <!-- 消息列表 -->
    <scroll-view scroll-y class="msg-list">
      <view v-for="item in msgList" :key="item.id" class="msg-item" @click="goChat(item)">
        <image :src="item.avatar" class="avatar" />
        <view class="msg-main">
          <view class="msg-title">{{item.title}}</view>
          <view class="msg-note">{{item.note}}</view>
        </view>
        <view class="msg-right">
          <view class="msg-time">{{item.time}}</view>
          <view v-if="item.unread" class="msg-unread">{{item.unread}}</view>
        </view>
      </view>
    </scroll-view>
    <!-- 底部TabBar -->
    <view class="tabbar">
      <view v-for="tab in tabs" :key="tab.name" :class="['tab', {active: tab.active}]" @click="switchTab(tab)">
        <text :class="['iconfont', tab.icon]"></text>
        <text class="tab-label">{{tab.label}}</text>
        <view v-if="tab.badge" class="tab-badge">{{tab.badge}}</view>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { ref } from 'vue';
const msgList = ref([
  { id: 1, title: '平台消息', note: '订单有新动态', time: '下午13:22', avatar: '/static/msg1.png', unread: 2 },
  { id: 2, title: '采购报价', note: '报价已回复', time: '上午10:12', avatar: '/static/msg2.png', unread: 1 },
  { id: 3, title: '张三', note: '你好', time: '昨天', avatar: '/static/avatar1.png', unread: 0 },
  { id: 4, title: '李四', note: '发来一张图片', time: '2023/12/01', avatar: '/static/avatar2.png', unread: 0 },
]);
const tabs = ref([
  { name: 'work', label: '工作', icon: 'icon-gongzuo', active: false },
  { name: 'msg', label: '消息', icon: 'icon-xiaoxi', active: true, badge: 3 },
  { name: 'contacts', label: '通讯录', icon: 'icon-tongxunlu', active: false },
  { name: 'mine', label: '我的', icon: 'icon-wode', active: false },
]);
function goChat(item) {
  if(item.title==='平台消息') {
    uni.navigateTo({ url: '/pages/notice/platform' });
  } else {
    uni.navigateTo({ url: '/pages/notice/chat?user=' + item.title });
  }
}
function switchTab(tab) {
  // 这里只做演示，实际应用可用uni.switchTab
  if(tab.name==='msg') return;
  uni.showToast({ title: '切换到' + tab.label, icon: 'none' });
}
</script>
<style scoped lang="scss">
.notice-index { min-height: 100vh; background: #f7f7f7; display: flex; flex-direction: column; }
.msg-list { flex: 1; }
.msg-item { display: flex; align-items: center; padding: 24rpx; border-bottom: 1px solid #eee; background: #fff; }
.avatar { width: 80rpx; height: 80rpx; border-radius: 50%; margin-right: 24rpx; }
.msg-main { flex: 1; }
.msg-title { font-weight: bold; font-size: 32rpx; color: #222; }
.msg-note { color: #888; font-size: 28rpx; margin-top: 8rpx; }
.msg-right { display: flex; flex-direction: column; align-items: flex-end; }
.msg-time { color: #bbb; font-size: 24rpx; }
.msg-unread { background: #ff3b30; color: #fff; border-radius: 50%; padding: 0 12rpx; font-size: 24rpx; margin-top: 8rpx; }
.tabbar { display: flex; height: 100rpx; background: #fff; border-top: 1px solid #eee; }
.tab { flex: 1; display: flex; flex-direction: column; align-items: center; justify-content: center; position: relative; color: #888; }
.tab.active { color: #1976d2; }
.tab-label { font-size: 24rpx; margin-top: 4rpx; }
.tab-badge { position: absolute; top: 8rpx; right: 32rpx; background: #ff3b30; color: #fff; border-radius: 50%; font-size: 20rpx; padding: 0 8rpx; }
</style> 