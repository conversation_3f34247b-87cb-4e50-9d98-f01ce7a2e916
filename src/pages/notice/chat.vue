<template>
  <view class="chat-page">
    <uni-nav-bar
      :border="false"
      title="聊天"
      color="#fff"
      left-icon="left"
      right-icon="more-filled"
      backgroundColor="#1976d2"
      @clickLeft="handleBack"
      @clickRight="goSetting"
    />
    <!-- 聊天内容 -->
    <scroll-view scroll-y class="chat-list">
      <view v-for="msg in msgList" :key="msg.id" :class="['chat-item', msg.self ? 'self' : '']">
        <image :src="msg.avatar" class="chat-avatar" />
        <view class="chat-bubble">{{msg.text}}</view>
      </view>
    </scroll-view>
    <!-- 输入区 -->
    <view class="chat-input-bar">
      <input v-model="input" class="chat-input" placeholder="请输入内容" />
      <button class="chat-send" @click="send">发送</button>
      <button class="chat-tool" @click="showTools = !showTools">+</button>
    </view>
    <!-- 附件/表情区 -->
    <view v-if="showTools" class="chat-tools">
      <view class="tool-btn">图片</view>
      <view class="tool-btn">拍照</view>
      <view class="tool-btn">文件</view>
      <view class="tool-btn">语音</view>
      <view class="tool-btn">名片</view>
      <view class="tool-btn">位置</view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { ref } from 'vue';
const msgList = ref([
  { id: 1, text: '你好，供应商李小叶', avatar: '/static/avatar1.png', self: false },
  { id: 2, text: '你好，有什么可以帮您？', avatar: '/static/avatar2.png', self: true },
  { id: 3, text: '你们的报价单发过来看看', avatar: '/static/avatar1.png', self: false },
  { id: 4, text: '好的，稍等！', avatar: '/static/avatar2.png', self: true },
]);
const input = ref('');
const showTools = ref(false);
function send() {
  if (!input.value) return;
  msgList.value.push({ id: Date.now(), text: input.value, avatar: '/static/avatar2.png', self: true });
  input.value = '';
}
function handleBack() { uni.navigateBack(); }
function goSetting() { uni.navigateTo({ url: '/pages/notice/chat-setting' }); }
</script>
<style scoped lang="scss">
.chat-page { min-height: 100vh; background: #f7f7f7; display: flex; flex-direction: column; }
.chat-list { flex: 1; padding: 24rpx; }
.chat-item { display: flex; align-items: flex-end; margin-bottom: 24rpx; }
.chat-item.self { flex-direction: row-reverse; }
.chat-avatar { width: 64rpx; height: 64rpx; border-radius: 50%; margin: 0 16rpx; }
.chat-bubble { max-width: 60vw; background: #fff; border-radius: 16rpx; padding: 16rpx 24rpx; font-size: 28rpx; color: #222; }
.chat-item.self .chat-bubble { background: #1976d2; color: #fff; }
.chat-input-bar { display: flex; align-items: center; padding: 16rpx; background: #fff; border-top: 1px solid #eee; }
.chat-input { flex: 1; border: 1px solid #eee; border-radius: 8rpx; padding: 12rpx; font-size: 28rpx; margin-right: 12rpx; }
.chat-send { background: #1976d2; color: #fff; border-radius: 8rpx; padding: 0 24rpx; }
.chat-tool { background: #eee; color: #1976d2; border-radius: 8rpx; margin-left: 8rpx; }
.chat-tools { display: flex; flex-wrap: wrap; background: #f7f7f7; padding: 16rpx; border-top: 1px solid #eee; }
.tool-btn { width: 25vw; text-align: center; padding: 24rpx 0; color: #1976d2; font-size: 28rpx; }
</style> 