<template>
  <view class="chat-setting-page">
    <uni-nav-bar
      :border="false"
      title="消息通知和设置"
      color="#fff"
      left-icon="left"
      backgroundColor="#1976d2"
      @clickLeft="handleBack"
    />
    <view class="setting-list">
      <view class="setting-item">
        <text class="setting-label">消息免打扰</text>
        <switch v-model="mute" />
      </view>
      <view class="setting-item">
        <text class="setting-label">置顶聊天</text>
        <switch v-model="top" />
      </view>
      <view class="setting-item">
        <text class="setting-label">新消息通知</text>
        <switch v-model="notify" />
      </view>
      <view class="setting-item">
        <text class="setting-label">消息显示详情</text>
        <switch v-model="showDetail" />
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { ref } from 'vue';
const mute = ref(true);
const top = ref(false);
const notify = ref(true);
const showDetail = ref(true);
function handleBack() { uni.navigateBack(); }
</script>
<style scoped lang="scss">
.chat-setting-page { min-height: 100vh; background: #f7f7f7; }
.setting-list { margin: 24rpx; background: #fff; border-radius: 12rpx; padding: 24rpx 0; }
.setting-item { display: flex; align-items: center; justify-content: space-between; padding: 24rpx; border-bottom: 1px solid #f0f0f0; font-size: 30rpx; }
.setting-item:last-child { border-bottom: none; }
.setting-label { color: #333; }
</style> 