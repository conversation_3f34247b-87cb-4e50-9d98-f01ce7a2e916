<template>
  <view class="contact-page">
    <uni-nav-bar
      :border="false"
      title="通讯录"
      color="#fff"
      backgroundColor="#1976d2"
      left-icon="left"
      @clickLeft="handleBack"
    />
    <scroll-view scroll-y class="contact-list">
      <view
        v-for="group in contactGroups"
        :key="group.alpha"
        class="contact-group"
      >
        <view class="group-alpha">{{ group.alpha }}</view>
        <view
          v-for="item in group.list"
          :key="item.id"
          class="contact-item"
          @click="goDetail(item)"
        >
          <text class="contact-name">{{ item.name }}</text>
          <button class="view-btn">详情</button>
        </view>
      </view>
    </scroll-view>
    <!-- 右侧字母索引 -->
    <view class="alpha-bar">
      <view v-for="alpha in alphaList" :key="alpha" class="alpha-item">{{
        alpha
      }}</view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { ref } from "vue";
const alphaList = ref([
  "A",
  "B",
  "C",
  "D",
  "E",
  "F",
  "G",
  "H",
  "I",
  "J",
  "K",
  "L",
  "M",
  "N",
  "O",
  "P",
  "Q",
  "R",
  "S",
  "T",
  "U",
  "V",
  "W",
  "X",
  "Y",
  "Z",
]);
const contactGroups = ref([
  { alpha: "A", list: [{ id: 1, name: "安琪" }] },
  { alpha: "B", list: [{ id: 2, name: "白雪" }] },
  { alpha: "C", list: [{ id: 3, name: "陈晨" }] },
]);
function handleBack() {
  uni.navigateBack();
}
function goDetail(item: any) {
  uni.navigateTo({ url: "/pages/client/detail?id=" + item.id });
}
</script>
<style scoped lang="scss">
.contact-page {
  min-height: 100vh;
  background: #f7f7f7;
  position: relative;
}
.contact-list {
  padding: 24rpx;
}
.contact-group {
  margin-bottom: 24rpx;
}
.group-alpha {
  color: #1976d2;
  font-weight: bold;
  font-size: 28rpx;
  margin-bottom: 8rpx;
}
.contact-item {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 8rpx;
  padding: 18rpx 24rpx;
  margin-bottom: 8rpx;
}
.contact-name {
  flex: 1;
  color: #222;
  font-size: 30rpx;
}
.view-btn {
  background: #1976d2;
  color: #fff;
  border-radius: 8rpx;
  padding: 0 18rpx;
  font-size: 26rpx;
}
.alpha-bar {
  position: fixed;
  right: 12rpx;
  top: 200rpx;
  display: flex;
  flex-direction: column;
  z-index: 10;
}
.alpha-item {
  color: #1976d2;
  font-size: 22rpx;
  padding: 4rpx 0;
  text-align: center;
}
</style>
