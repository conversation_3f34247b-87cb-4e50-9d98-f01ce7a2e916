<template>
  <view class="plan-page">
    <uni-nav-bar
      :border="false"
      title="预约"
      color="#fff"
      left-icon="left"
      right-text="创建"
      @clickLeft="handleClickLeft"
      @clickRight="handleClickRight"
      backgroundColor="var(--primary-color)"
    ></uni-nav-bar>
    <scroll-view class="plan-manage">
      <l-calendar
        :markedDays="markedDays"
        @dayClick="handleDayClick"
        @monthChange="handleMonthChange"
      />
      <view class="plan-list">
        <view class="plan-list-item" v-for="item in planList" :key="item.id">
          <view class="plan-list-item-top flex-sbw">
            <view class="plan-list-item-top-name">
              {{ item.title }}
            </view>
            <view class="plan-list-item-top-type">
              类别：
              <text class="plan-list-item-top-type-text">{{item.category.title}}</text>
            </view>
          </view>
          <view class="plan-list-item-content">
            <view class="plan-list-item-content-icon"> </view>
            <view class="plan-list-item-content-text">
              {{ item.content }}
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import LCalendar from "@/components/lCalendar/lCalendar.vue";
import { getPlanCategoryList, getCalendarPlan, getPlanList } from "@/api/plan";
import type { Response } from "@/utils/type";
interface PlanList {
  id: number;
  title: string;
  car_license_code?: string;
  content?: string;
  [key: string]: any;
}

const handleClickLeft = () => {
  uni.navigateBack();
};
const handleClickRight = () => {
  uni.navigateTo({
    url: "/pages/client/createPlan",
  });
};
const markedDays = ref([]);
const planList = ref<PlanList[]>([]);
const planCategoryList = ref([]);
let currentYear = new Date().getFullYear();
let currentMonth = String(new Date().getMonth() + 1).padStart(2, "0");
let currentDay = String(new Date().getDate()).padStart(2, "0");
const yearMonth = ref(`${currentYear}-${currentMonth}`);
const yearMonthDay = ref(`${currentYear}-${currentMonth}-${currentDay}`);
const handleDayClick = (day: any) => {
  yearMonthDay.value = day;
  getPlanListData();
};
const handleMonthChange = (year_month: string) => {
  yearMonth.value = year_month;
  getCalendarPlanData();
};
const getPlanCategoryData = async () => {
  const { data, code }: Response = (await getPlanCategoryList({})) as Response;
  if (code === 200) {
    planCategoryList.value = data;
  }
};
const getCalendarPlanData = async () => {
  const { data, code }: Response = (await getCalendarPlan({
    month: yearMonth.value,
  })) as Response;
  if (code === 200) {
    markedDays.value = data
      .filter((item: any) => item.num)
      .map((item: any) => item.date);
  }
};
const getPlanListData = async () => {
  const { data, code }: Response = (await getPlanList({
    withdrawal_time: yearMonth.value,
  })) as Response;
  if (code === 200) {
    planList.value = data.list;
  }
};
onMounted(() => {
  getPlanCategoryData();
  getCalendarPlanData();
});
</script>

<style scoped lang="less">
.plan-manage {
  .plan-list {
    padding: 3.5vw;
    &-item {
      padding: 0 2.8vw;
      border-radius: 0.56vw;
      box-shadow: 0 0.33vw 0.5vw 0.5vw #e8e2e2;
      &-top {
        height: 8.8vw;
        &-name {
          font-size: 3.47vw;
          color: #333333;
        }
        &-type {
          font-size: 3.47vw;
          color: #333333;
          &-text {
            color: var(--primary-color);
          }
        }
      }
      &-content {
        display: flex;
        align-items: center;
        &-icon {
          width: 5.73vw;
          height: 5.73vw;
          border-radius: 50%;
          flex-shrink: 0;
          margin-right: 4.2vw;
          background-color: var(--primary-color);
        }
        min-height: 17.2vw;
        font-size: 2.93vw;
        color: #333333;
        line-height: 6.4vw;
      }
    }
  }
}
</style>
