<template>
  <view class="client-page">
    <uni-nav-bar
      :border="false"
      title="车店助手"
      color="#fff"
      backgroundColor="var(--primary-color)"
    ></uni-nav-bar>
    <l-table :headers="headers" :fetchData="fetchData">
      <template #action="{ row }">
        <view>操作{{ row.name }}</view>
      </template>
    </l-table>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { TableColumn, TableData } from "@/utils/type";
import lTable from "@/components/lTable/lTable.vue";
const value = ref("");
const headers: TableColumn[] = [
  {
    title: "日期",
    columnKey: "date",
    width: "100upx",
  },
  {
    title: "姓名",
    columnKey: "name",
    width: "100px",
  },
  {
    title: "操作",
    columnKey: "action",
    width: "200px",
    slot: "action",
  },
];
const fetchData = (): Promise<TableData[]> => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve([
        {
          date: "2020-10-20",
          name: "<PERSON><PERSON>",
        },
        {
          date: "2020-10-21",
          name: "HanMeiMei",
        },
        {
          date: "2020-10-22",
          name: "LiLei",
        },
        {
          date: "2020-10-23",
          name: "Danner",
        },
      ]);
    }, 1000);
  });
};
</script>

<style lang="scss" scoped>
.client-page {
  height: 100%;
}
</style>
