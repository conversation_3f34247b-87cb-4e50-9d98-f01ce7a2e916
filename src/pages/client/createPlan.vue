<template>
  <view class="plan-create">
    <uni-nav-bar
      :border="false"
      title="新建计划"
      color="#fff"
      left-icon="left"
      @clickLeft="handleClickLeft"
      backgroundColor="var(--primary-color)"
    ></uni-nav-bar>
    <view class="plan-manage">
      <view class="plan-card">
        <view class="plan-row flex-sbw">
          <view class="plan-row-label"> 服务类别 </view>
          <picker
            class="plan-row-picker"
            @change="bindPickerChange"
            :value="planCategoryIndex"
            :range="planCategoryList"
            range-key="title"
          >
            <view class="plan-row-value">
              <text v-if="planCategoryList[planCategoryIndex]">{{
                planCategoryList[planCategoryIndex]?.title
              }}</text>
              <text v-else class="plan-row-value-placeholder">请选择</text>
              <uni-icons
                class="plan-row-value-icon"
                type="right"
                size="15"
              ></uni-icons
            ></view>
          </picker>
        </view>
        <view class="plan-row flex-sbw">
          <view class="plan-row-label"> 事件等级 </view>
          <picker
            class="plan-row-picker"
            @change="bindLevelPickerChange"
            :value="levelIndex"
            :range="levelArray"
          >
            <view class="plan-row-value">
              <text class="level-icon" :class="`level-${levelIndex}`"></text>
            </view>
          </picker>
        </view>
        <view class="plan-row flex-sbw">
          <view class="plan-row-label"> 计划时间 </view>
          <view class="plan-row-value">
            <lDateTimePicker
              style="width: 40vw"
              v-model="dateTime"
              title="请选择日期时间"
              :showTime="true"
          /></view>
        </view>
        <view class="plan-row flex-sbw">
          <view class="plan-row-label"> 提醒标题 </view>
          <view class="plan-row-value">
            <input
              v-model="title"
              class="plan-row-input"
              placeholder-class="plan-row-textarea-placeholder"
              placeholder="请输入提醒标题"
            />
          </view>
        </view>
        <view class="plan-row flex-sbw">
          <view class="plan-row-label"> 提醒方式 </view>
          <picker
            class="plan-row-picker"
            @change="bindRemindTypePickerChange"
            :value="remindTypeIndex"
            :range="remindTypeArray"
          >
            <view class="plan-row-value">
              <text v-if="remindTypeArray[remindTypeIndex]">{{
                remindTypeArray[remindTypeIndex]
              }}</text>
              <text v-else class="plan-row-value-placeholder">请选择</text>
              <uni-icons
                class="plan-row-value-icon"
                type="right"
                size="15"
              ></uni-icons
            ></view>
          </picker>
        </view>
        <!-- <view class="plan-row flex-sbw">
          <view class="plan-row-label"> 关联车主 </view>
          <picker
            class="plan-row-picker"
            @change="bindPickerChange"
            :value="index"
            :range="array"
          >
            <view class="plan-row-value">
              <text v-if="array[index]">{{ array[index] }}</text>
              <text v-else class="plan-row-value-placeholder">请选择</text>
              <uni-icons
                class="plan-row-value-icon"
                type="right"
                size="15"
              ></uni-icons
            ></view>
          </picker>
        </view> -->
        <view class="plan-row textarea-row">
          <view class="plan-row-label"> 计划备注 </view>
          <view class="plan-row-textarea">
            <textarea
              v-model="remark"
              placeholder="请输入提醒详细内容"
              placeholder-class="plan-row-textarea-placeholder"
            />
          </view>
        </view>
      </view>
      <!-- <view class="plan-card" style="margin-top: 3.5vw">
        <view class="plan-row flex-sbw">
          <view class="plan-row-label"> 标注状态 </view>
          <picker
            class="plan-row-picker"
            @change="bindPickerChange"
            :value="index"
            :range="array"
          >
            <view class="plan-row-value">
              <text v-if="array[index]">{{ array[index] }}</text>
              <text v-else class="plan-row-value-placeholder">请选择</text>
              <uni-icons
                class="plan-row-value-icon"
                type="right"
                size="15"
              ></uni-icons
            ></view>
          </picker>
        </view>
      </view> -->
    </view>
    <view class="plan-btn">
      <view class="btn-list">
        <view class="btn-item" @tap="handleSave"> 保存 </view>
        <view class="btn-item color-warning" v-show="true"> 接车开单 </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import LDateTimePicker from "@/components/lDateTimePicker/lDateTimePicker.vue";
import { ref, reactive, onMounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { getPlanCategoryList, addPlan } from "@/api/plan";
import type { Response } from "@/utils/type";
import { formatTime } from "@/utils/time";
interface PlanCategory {
  id: number;
  title: string;
}
const dateTime = ref("");
const handleClickLeft = () => {
  uni.navigateBack();
};
const title = ref("");
const remark = ref("");
const levelArray = ["普通", "稍急", "紧急"];
const levelIndex = ref(0);
const planCategoryList = ref<PlanCategory[]>([]);
const planCategoryIndex = ref();
const remindTypeArray = ["提前一天", "提前两天", "提前三天"];
const remindTypeIndex = ref();
const bindPickerChange = (e: any) => {
  planCategoryIndex.value = e.detail.value;
};
const bindLevelPickerChange = (e: any) => {
  levelIndex.value = e.detail.value;
};
const bindRemindTypePickerChange = (e: any) => {
  remindTypeIndex.value = e.detail.value;
};
const getPlanCategoryListData = async () => {
  const { data, code }: Response = (await getPlanCategoryList({})) as Response;
  if (code === 200) {
    planCategoryList.value = data.list;
  }
};
// 保存
const handleSave = async () => {
  let reminder_time_str;
  // 根据预约时间，计算提醒时间
  if (dateTime.value && remindTypeIndex.value !== undefined) {
    const reminder_time = new Date(dateTime.value);
    reminder_time.setDate(reminder_time.getDate() - remindTypeIndex.value - 1);
    reminder_time_str = formatTime(reminder_time.toLocaleString());
  }
  const params = {
    title: title.value, // 预约标题
    category_id: planCategoryList.value[planCategoryIndex.value].id, // 服务类别ID
    event_level: levelIndex.value + 1, //事件等级1普通2稍急3紧急
    withdrawal_time: dateTime.value, // 计划时间(预约提醒时间) 例如2020-01-04 01:01:01
    reminder_method: remindTypeArray[remindTypeIndex.value], // 提醒方式 例如提前3天
    reminder_time: reminder_time_str,
    // customer_user_id: 103, // 关联车主的ID
    // customer_car_id: 24011, //关联车辆ID
    content: remark.value, //提醒内容
    // categoryId: planCategoryIndex.value,
    // level: levelIndex.value,
    // dateTime: dateTime.value,
    // remindType: remindTypeIndex.value,
    // remark: remark.value,
  };
  const { data, code }: Response = (await addPlan(params)) as Response;
  if (code == 200) {
    uni.showToast({
      icon: "none",
      title: "保存成功",
    });
  }
  console.log(dateTime.value);
};

onLoad((options) => {
  getPlanCategoryListData();
});
</script>

<style scoped lang="scss">
.plan-create {
  height: 100%;
  .plan-manage {
    height: calc(100% - 44px);
    padding: 3.5vw;
    box-sizing: border-box;
    .plan-card {
      border-radius: 0.56vw;
      box-shadow: 0 0.33vw 0.5vw 0.5vw #e8e2e2;
      .plan-row {
        height: 10.6vw;
        box-sizing: border-box;
        padding: 0 3.5vw;
        color: #333333;

        &-label {
          font-size: 3.47vw;
        }
        &-value {
          font-size: 3.47vw;
          text-align: right;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          &-icon {
            margin-left: 1.33vw;
          }
          &-placeholder {
            color: #909090;
          }
          .level-icon {
            width: 5.73vw;
            height: 5.73vw;
            background-color: var(--primary-color);
            border-radius: 50%;
            &.level-0 {
              background-color: var(--primary-color);
            }
            &.level-1 {
              background-color: var(--warning-color);
            }
            &.level-2 {
              background-color: var(--danger-color);
            }
          }
        }
        &-picker {
          width: 30vw;
        }
        &-textarea {
          // min-height: 20.6vw;
          padding: 3.33vw 0;
          box-sizing: border-box;
          &-placeholder {
            color: #909090;
            font-size: 3.47vw;
          }
          :deep(uni-textarea) {
            height: 30vw;
          }

          :deep(.uni-textarea-textarea) {
            font-size: 3.47vw;
            color: #333333;
          }
        }
        &.textarea-row {
          padding-top: 2.33vw;
          height: auto;
        }
      }
    }
  }
  .plan-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 15.33vw;
    .btn-list {
      height: 100%;
      display: flex;
      justify-content: space-between;
      .btn-item {
        width: 50%;
        height: 100%;
        flex: 1;
        background-color: var(--primary-color);
        color: #fff;
        font-size: 4.8vw;
        text-align: center;
        line-height: 15.33vw;
        &.color-warning {
          background-color: var(--warning-color);
        }
      }
    }
  }
}
</style>
