<template>
  <uni-nav-bar
    :border="false"
    title="选择服务"
    color="#fff"
    left-icon="left"
    @clickLeft="handleClickLeft"
    backgroundColor="var(--primary-color)"
  ></uni-nav-bar>
  <view class="warehouse-manage">
    <view class="warehouse-manage-item">
      <view class="warehouse-manage-item-title">选择服务</view>
    </view>
  </view>
</template>

<script setup lang="ts">
const handleClickLeft = () => {
  uni.navigateBack();
};
</script>

<style scoped lang="less">
.warehouse-manage {
  padding: 20rpx;
}
</style>
