<template>
  <view class="client-add">
    <uni-nav-bar
      :border="false"
      title="新增/编辑客户"
      color="#fff"
      backgroundColor="#1976d2"
      left-icon="left"
      @clickLeft="handleBack"
    />
    <view class="form-section">
      <view class="form-item">
        <text class="label">姓名：</text>
        <input v-model="form.name" class="input" placeholder="请输入姓名" />
      </view>
      <view class="form-item">
        <text class="label">电话：</text>
        <input v-model="form.phone" class="input" placeholder="请输入手机号" />
      </view>
      <view class="form-item">
        <text class="label">性别：</text>
        <picker
          :range="['男', '女']"
          @change="(e) => (form.gender = ['男', '女'][e.detail.value])"
        >
          <view class="picker">{{ form.gender || "请选择" }}</view>
        </picker>
      </view>
      <view class="form-item">
        <text class="label">生日：</text>
        <picker
          mode="date"
          :value="form.birthday"
          @change="(e) => (form.birthday = e.detail.value)"
        >
          <view class="picker">{{ form.birthday || "请选择" }}</view>
        </picker>
      </view>
      <view class="form-item">
        <text class="label">车牌：</text>
        <input v-model="form.car" class="input" placeholder="请输入车牌号" />
      </view>
      <view class="form-item">
        <text class="label">备注：</text>
        <input v-model="form.remark" class="input" placeholder="备注" />
      </view>
    </view>
    <view class="footer-btn">
      <button class="save-btn" @click="save">保存</button>
    </view>
  </view>
</template>
<script setup lang="ts">
import { ref } from "vue";
const form = ref({
  name: "",
  phone: "",
  gender: "",
  birthday: "",
  car: "",
  remark: "",
});
function handleBack() {
  uni.navigateBack();
}
function save() {
  uni.showToast({ title: "保存成功", icon: "success" });
}
</script>
<style scoped lang="scss">
.client-add {
  min-height: 100vh;
  background: #f7f7f7;
}
.form-section {
  background: #fff;
  margin: 24rpx;
  border-radius: 12rpx;
  padding: 24rpx 0;
}
.form-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1px solid #f0f0f0;
  font-size: 30rpx;
}
.form-item:last-child {
  border-bottom: none;
}
.label {
  color: #333;
  width: 120rpx;
}
.input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 30rpx;
  color: #222;
}
.picker {
  flex: 1;
  color: #1976d2;
}
.footer-btn {
  padding: 32rpx;
}
.save-btn {
  width: 100%;
  background: #1976d2;
  color: #fff;
  border-radius: 12rpx;
  font-size: 32rpx;
  padding: 24rpx 0;
}
</style>
