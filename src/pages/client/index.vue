<template>
  <view class="contact-page">
    <!-- 顶部导航栏 -->
    <uni-nav-bar
      :border="false"
      title="客户管理"
      color="#fff"
      left-icon="left"
      right-text="新增"
      @clickLeft="onBack"
      @clickRight="onAdd"
      backgroundColor="var(--primary-color)"
    >
    </uni-nav-bar>

    <!-- 搜索框 -->
    <view class="search-box">
      <uni-search-bar
        v-model="searchText"
        placeholder="请输入人名、客户名称、手机号码查询"
        radius="100"
        cancelButton="none"
        :focus="true"
      />
    </view>

    <!-- 空状态 -->
    <view v-if="!hasContacts" class="empty-state"> 暂无数据 </view>

    <!-- 联系人列表 -->
    <scroll-view
      class="contact-list"
      scroll-y
      :style="{ height: scrollHeight + 'px' }"
      @scrolltolower="loadMore"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
    >
      <view
        v-for="contact in filteredContacts"
        :key="contact.id"
        class="contact-item"
      >
        <view class="contact-main">
          <view class="contact-left">
            <view class="name-row">
              <text class="name">{{ contact.name }}</text>
              <text class="phone">{{ contact.phone }}</text>
              <text v-if="contact.tag === 'VIP'" class="tag vip">VIP</text>
              <text v-if="contact.tag === 'P'" class="tag p">P</text>
            </view>
            <view class="car-row">
              <text class="car-info">{{ contact.carInfo }}</text>
              <view class="action-buttons">
                <button class="action-btn" @tap="handleSales(contact)">
                  统销
                </button>
                <button class="action-btn" @tap="handleCard(contact)">
                  开卡
                </button>
                <button class="action-btn" @tap="handleDetails(contact)">
                  详情
                </button>
              </view>
            </view>
          </view>
          <view class="contact-right">
            <button class="action-btn primary" @tap="handleCall(contact)">
              开单
            </button>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";

// 搜索文本
const searchText = ref("");
const scrollHeight = ref(0);
const isRefreshing = ref(false);
const page = ref(1);
const pageSize = 10;
const hasMore = ref(true);
const loading = ref(false);

// 模拟联系人数据源
const allContacts = [
  {
    id: 1,
    name: "张贺然",
    phone: "158****5568",
    carInfo: "粤CF521D",
    tag: "VIP",
  },
  {
    id: 2,
    name: "四川省旅游股份公司",
    phone: "158****5568",
    carInfo: "粤CF521D",
  },
  {
    id: 3,
    name: "赵国",
    phone: "158****5568",
    carInfo: "粤CDF521D",
    tag: "P",
  },
  {
    id: 4,
    name: "上谷套装",
    phone: "158****5568",
    carInfo: "粤CF521D",
    tag: "VIP",
  },
  {
    id: 5,
    name: "成都乐华时尚公司",
    phone: "158****5568",
    carInfo: "粤CF521D",
    tag: "P",
  },
  // 添加更多测试数据
  ...Array.from({ length: 15 }, (_, i) => ({
    id: i + 6,
    name: `测试客户${i + 1}`,
    phone: "158****5568",
    carInfo: "粤CF521D",
    tag: i % 2 === 0 ? "VIP" : "P",
  })),
];

// 联系人列表
const contacts = ref([]);

// 初始化数据
const initData = () => {
  contacts.value = [];
  page.value = 1;
  hasMore.value = true;
  loadData();
};

// 加载数据
const loadData = () => {
  if (!hasMore.value || loading.value) return;
  
  loading.value = true;
  // 模拟接口请求
  setTimeout(() => {
    const start = (page.value - 1) * pageSize;
    const end = start + pageSize;
    const newData = allContacts.slice(start, end);
    
    if (newData.length < pageSize) {
      hasMore.value = false;
    }
    
    contacts.value = [...contacts.value, ...newData];
    page.value++;
    loading.value = false;
  }, 500);
};

// 过滤后的联系人列表
const filteredContacts = computed(() => {
  if (!searchText.value) return contacts.value;
  return contacts.value.filter(
    (contact) =>
      contact.name.includes(searchText.value) ||
      contact.phone.includes(searchText.value) ||
      contact.carInfo.includes(searchText.value)
  );
});

// 是否有联系人
const hasContacts = computed(() => filteredContacts.value.length > 0);

// 计算滚动区域高度
const initScrollHeight = () => {
  const { windowHeight } = uni.getSystemInfoSync();
  const query = uni.createSelectorQuery();
  query
    .select(".search-box")
    .boundingClientRect((data) => {
      const searchHeight = data.height;
      scrollHeight.value = windowHeight - searchHeight;
    })
    .exec();
};

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true;
  // 重置数据并重新加载
  initData();
  setTimeout(() => {
    isRefreshing.value = false;
    uni.showToast({
      title: "刷新成功",
      icon: "none",
    });
  }, 1000);
};

// 上拉加载更多
const loadMore = () => {
  if (hasMore.value && !loading.value) {
    loadData();
  }
};

// 页面加载时初始化
onMounted(() => {
  initScrollHeight();
  initData();
});

// 返回上一页
const onBack = () => {
  uni.navigateBack();
};

// 新增客户
const onAdd = () => {
  uni.showToast({
    title: "新增客户",
    icon: "none",
  });
};

// 处理开单
const handleCall = (contact) => {
  uni.showToast({
    title: `开单: ${contact.name}`,
    icon: "none",
  });
};

// 处理统销
const handleSales = (contact) => {
  uni.showToast({
    title: `统销: ${contact.name}`,
    icon: "none",
  });
};

// 处理开卡
const handleCard = (contact) => {
  uni.showToast({
    title: `开卡: ${contact.name}`,
    icon: "none",
  });
};

// 处理查看详情
const handleDetails = (contact) => {
  uni.showToast({
    title: `查看详情: ${contact.name}`,
    icon: "none",
  });
};
</script>

<style scoped>
.contact-page {
  min-height: 100vh;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.search-box {
  background: #fff;
  padding: 20rpx;
  /* 固定搜索框 */
  position: sticky;
  top: 0;
  z-index: 1;
}

.contact-list {
  flex: 1;
  height: 0; /* 关键：让scroll-view能够正确计算高度 */
}

.contact-item {
  background: #fff;
  padding: 32rpx 30rpx;
  border-bottom: 16rpx solid #f5f5f5;
}

.contact-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.contact-left {
  flex: 1;
  margin-right: 24rpx;
}

.contact-right {
  display: flex;
  align-items: center;
}

.name-row {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.name {
  font-size: 32rpx;
  color: #333;
  margin-right: 24rpx;
}

.phone {
  font-size: 28rpx;
  color: #666;
}

.tag {
  margin-left: 16rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  font-size: 20rpx;
}

.vip {
  background: #e1f1ff;
  color: #1989fa;
}

.p {
  background: #ffe7e7;
  color: #ff4d4f;
}

.car-row {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.car-info {
  font-size: 28rpx;
  color: #666;
  background: #f7f8fa;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  margin: 0;
  font-size: 24rpx;
  padding: 0 20rpx;
  height: 48rpx;
  line-height: 44rpx;
  border-radius: 4rpx;
  background: #fff;
  color: #666;
  border: 2rpx solid #ddd;
}

.action-btn.primary {
  background: #1989fa;
  color: #fff;
  border: none;
  height: 56rpx;
  line-height: 52rpx;
  padding: 0 32rpx;
}

/* 自定义搜索框样式 */
:deep(.uni-searchbar) {
  padding: 0;
}

:deep(.uni-searchbar__box) {
  border-radius: 8rpx;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 添加加载状态样式 */
.loading {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 24rpx;
}

.no-more {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 24rpx;
}
</style>
