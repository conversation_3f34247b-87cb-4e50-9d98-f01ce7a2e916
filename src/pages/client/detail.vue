<template>
  <view class="client-detail">
    <uni-nav-bar
      :border="false"
      title="客户详情"
      color="#fff"
      backgroundColor="#1976d2"
      left-icon="left"
      @clickLeft="handleBack"
    />
    <view class="user-section">
      <image :src="user.avatar" class="user-avatar" />
      <view class="user-info">
        <view class="user-name">{{ user.name }}</view>
        <view class="user-phone">{{ user.phone }}</view>
        <view class="user-meta">注册时间：{{ user.regTime }}</view>
      </view>
    </view>
    <view class="stat-section">
      <view class="stat-item">
        <text class="stat-label">累计消费</text>
        <text class="stat-value">{{ user.totalConsume }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">储值余额</text>
        <text class="stat-value">{{ user.balance }}</text>
      </view>
      <view class="stat-item">
        <text class="stat-label">积分</text>
        <text class="stat-value">{{ user.score }}</text>
      </view>
    </view>
    <view class="car-section">
      <view class="car-title">车辆信息</view>
      <view
        v-for="car in user.cars"
        :key="car.id"
        class="car-item"
        @click="goCar(car)"
      >
        <text class="car-plate">{{ car.plate }}</text>
        <text class="car-model">{{ car.model }}</text>
        <button class="car-btn" @click.stop="showCarDialog(car)">详情</button>
      </view>
    </view>
    <view class="action-section">
      <button class="action-btn" @click="goConsume">消费记录</button>
      <button class="action-btn" @click="goEdit">编辑客户</button>
    </view>
    <!-- 车辆弹窗组件占位，实际开发可用弹窗/抽屉实现 -->
    <car-dialog
      v-if="carDialogVisible"
      :car="currentCar"
      @close="carDialogVisible = false"
    />
  </view>
</template>
<script setup lang="ts">
import { ref } from "vue";
// 假设 car-dialog.vue 已实现弹窗组件
// import CarDialog from './car-dialog.vue';
const user = ref({
  avatar: "/static/avatar1.png",
  name: "张三",
  phone: "158****5555",
  regTime: "2024/01/18",
  totalConsume: "37500.00",
  balance: "5120.00",
  score: 60,
  cars: [
    { id: 1, plate: "川A·GSY67", model: "奥迪A6L" },
    { id: 2, plate: "川A·D25367", model: "宝马X3" },
  ],
});
const carDialogVisible = ref(false);
const currentCar = ref(null);
function handleBack() {
  uni.navigateBack();
}
function goEdit() {
  uni.navigateTo({ url: "/pages/client/add?id=1" });
}
function goConsume() {
  uni.navigateTo({ url: "/pages/client/consume-list?user=1" });
}
function goCar(car: any) {
  uni.navigateTo({ url: "/pages/client/car-detail?id=" + car.id });
}
function showCarDialog(car: any) {
  currentCar.value = car;
  carDialogVisible.value = true;
}
</script>
<style scoped lang="scss">
.client-detail {
  min-height: 100vh;
  background: #f7f7f7;
}
.user-section {
  display: flex;
  align-items: center;
  background: #fff;
  padding: 32rpx;
}
.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 32rpx;
}
.user-info {
  flex: 1;
}
.user-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #222;
}
.user-phone {
  color: #1976d2;
  font-size: 28rpx;
  margin: 8rpx 0;
}
.user-meta {
  color: #888;
  font-size: 24rpx;
}
.stat-section {
  display: flex;
  background: #fff;
  margin: 24rpx;
  border-radius: 12rpx;
  padding: 24rpx 0;
}
.stat-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.stat-label {
  color: #888;
  font-size: 26rpx;
}
.stat-value {
  color: #1976d2;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 8rpx;
}
.car-section {
  background: #fff;
  margin: 24rpx;
  border-radius: 12rpx;
  padding: 24rpx;
}
.car-title {
  font-weight: bold;
  color: #1976d2;
  font-size: 30rpx;
  margin-bottom: 16rpx;
}
.car-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}
.car-plate {
  color: #1976d2;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 16rpx;
}
.car-model {
  color: #333;
  font-size: 28rpx;
  flex: 1;
}
.car-btn {
  background: #eee;
  color: #1976d2;
  border-radius: 8rpx;
  padding: 0 16rpx;
}
.action-section {
  display: flex;
  gap: 24rpx;
  margin: 32rpx 24rpx;
}
.action-btn {
  flex: 1;
  background: #1976d2;
  color: #fff;
  border-radius: 12rpx;
  font-size: 30rpx;
  padding: 20rpx 0;
}
</style>
