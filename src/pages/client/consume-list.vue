<template>
  <view class="consume-list-page">
    <uni-nav-bar
      :border="false"
      title="消费记录"
      color="#fff"
      backgroundColor="#1976d2"
      left-icon="left"
      @clickLeft="handleBack"
    />
    <view class="consume-table">
      <view class="consume-header">
        <text class="th">序号</text>
        <text class="th">时间</text>
        <text class="th">订单编号</text>
        <text class="th">金额</text>
        <text class="th">操作</text>
      </view>
      <view
        v-for="(item, idx) in consumeList"
        :key="item.id"
        class="consume-row"
      >
        <text class="td">{{ idx + 1 }}</text>
        <text class="td">{{ item.time }}</text>
        <text class="td">{{ item.orderNo }}</text>
        <text class="td">{{ item.amount }}</text>
        <button class="td detail-btn" @click="goDetail(item)">详情</button>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { ref } from "vue";
const consumeList = ref([
  {
    id: 1,
    time: "07.29 15:32",
    orderNo: "KD202407091532...",
    amount: "1500.00",
  },
  {
    id: 2,
    time: "07.29 15:32",
    orderNo: "KD202407091532...",
    amount: "800.00",
  },
]);
function handleBack() {
  uni.navigateBack();
}
function goDetail(item: any) {
  uni.showToast({ title: "查看详情：" + item.orderNo, icon: "none" });
}
</script>
<style scoped lang="scss">
.consume-list-page {
  min-height: 100vh;
  background: #f7f7f7;
}
.consume-table {
  background: #fff;
  margin: 24rpx;
  border-radius: 12rpx;
}
.consume-header,
.consume-row {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  padding: 16rpx 0;
}
.consume-header {
  font-weight: bold;
  background: #f7f7f7;
  border-radius: 12rpx 12rpx 0 0;
}
.th,
.td {
  flex: 1;
  text-align: center;
}
.detail-btn {
  background: #1976d2;
  color: #fff;
  border-radius: 8rpx;
  padding: 0 16rpx;
}
</style>
