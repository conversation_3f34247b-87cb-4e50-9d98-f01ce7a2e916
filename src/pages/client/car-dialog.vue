<template>
  <view class="car-dialog-mask" @click.self="$emit('close')">
    <view class="car-dialog">
      <view class="car-dialog-title">车辆详情</view>
      <view class="car-dialog-row"><text>车牌：</text>{{ car?.plate }}</view>
      <view class="car-dialog-row"><text>车型：</text>{{ car?.model }}</view>
      <view class="car-dialog-row"><text>年检到期：</text>2025-08-18</view>
      <view class="car-dialog-row"><text>保险公司：</text>平安保险</view>
      <view class="car-dialog-row"><text>保险到期：</text>2025-08-18</view>
      <view class="car-dialog-row"><text>当前里程：</text>90000km</view>
      <view class="car-dialog-row">
        <text>次数：</text>
        <button class="num-btn" @click="count = Math.max(1, count - 1)">
          -
        </button>
        <text class="num">{{ count }}</text>
        <button class="num-btn" @click="count = count + 1">+</button>
      </view>
      <view class="car-dialog-actions">
        <button class="car-dialog-btn" @click="$emit('close')">关闭</button>
        <button class="car-dialog-btn primary">编辑</button>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { ref, defineProps } from "vue";
const props = defineProps<{ car: any }>();
const count = ref(1);
</script>
<style scoped lang="scss">
.car-dialog-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.car-dialog {
  background: #fff;
  border-radius: 16rpx;
  width: 600rpx;
  padding: 32rpx;
}
.car-dialog-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 24rpx;
}
.car-dialog-row {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}
.num-btn {
  background: #eee;
  color: #1976d2;
  border-radius: 8rpx;
  padding: 0 16rpx;
  margin: 0 8rpx;
}
.num {
  width: 40rpx;
  text-align: center;
}
.car-dialog-actions {
  display: flex;
  gap: 24rpx;
  margin-top: 32rpx;
}
.car-dialog-btn {
  flex: 1;
  background: #eee;
  color: #1976d2;
  border-radius: 8rpx;
  font-size: 28rpx;
  padding: 16rpx 0;
}
.car-dialog-btn.primary {
  background: #1976d2;
  color: #fff;
}
</style>
