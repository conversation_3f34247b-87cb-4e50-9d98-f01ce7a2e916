<template>
  <view class="car-detail-page">
    <uni-nav-bar
      :border="false"
      title="车辆详情"
      color="#fff"
      backgroundColor="#1976d2"
      left-icon="left"
      @clickLeft="handleBack"
    />
    <view class="car-info-section">
      <view class="car-info-row"><text>车牌：</text>川A·GSY67</view>
      <view class="car-info-row"><text>车型：</text>奥迪A6L</view>
      <view class="car-info-row"><text>年检到期：</text>2025-08-18</view>
      <view class="car-info-row"><text>保险公司：</text>平安保险</view>
      <view class="car-info-row"><text>保险到期：</text>2025-08-18</view>
      <view class="car-info-row"><text>当前里程：</text>90000km</view>
    </view>
    <view class="record-section">
      <view class="record-title">维修/消费记录</view>
      <view
        v-for="item in recordList"
        :key="item.id"
        class="record-item"
        @click="goRecord(item)"
      >
        <text class="record-date">{{ item.date }}</text>
        <text class="record-type">{{ item.type }}</text>
        <text class="record-amount">{{ item.amount }}</text>
        <button class="record-btn">详情</button>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import { ref } from "vue";
const recordList = ref([
  { id: 1, date: "2024/05/15 11:59", type: "保养", amount: "1500.00" },
  { id: 2, date: "2024/04/05 11:59", type: "维修", amount: "800.00" },
]);
function handleBack() {
  uni.navigateBack();
}
function goRecord(item: any) {
  uni.navigateTo({ url: "/pages/client/consume-list?id=" + item.id });
}
</script>
<style scoped lang="scss">
.car-detail-page {
  min-height: 100vh;
  background: #f7f7f7;
}
.car-info-section {
  background: #fff;
  margin: 24rpx;
  border-radius: 12rpx;
  padding: 24rpx;
}
.car-info-row {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 12rpx;
}
.record-section {
  background: #fff;
  margin: 24rpx;
  border-radius: 12rpx;
  padding: 24rpx;
}
.record-title {
  font-weight: bold;
  color: #1976d2;
  font-size: 30rpx;
  margin-bottom: 16rpx;
}
.record-item {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}
.record-date {
  color: #888;
  font-size: 28rpx;
  width: 220rpx;
}
.record-type {
  color: #333;
  font-size: 28rpx;
  flex: 1;
}
.record-amount {
  color: #ff9900;
  font-size: 28rpx;
  width: 120rpx;
}
.record-btn {
  background: #eee;
  color: #1976d2;
  border-radius: 8rpx;
  padding: 0 16rpx;
}
</style>
