<template>
  <uni-popup ref="popup" type="right" :mask-click="false">
    <view class="permission-config">
      <view class="header">
        <text class="title">权限配置</text>
      </view>
      
      <scroll-view scroll-y class="content">
        <view v-for="(section, key) in permissionData" :key="key" class="section">
          <view class="section-header" @click="toggleSection(key)">
            <text class="section-title">{{ section.title }}</text>
            <uni-icons :type="expandedSections[key] ? 'bottom' : 'right'" size="16" color="#666" />
          </view>
          
          <view v-if="expandedSections[key]" class="permission-list">
            <view v-for="permission in section.permissions" 
                  :key="permission.id" 
                  class="permission-item">
              <checkbox-group @change="togglePermission(key, permission.id)">
                <checkbox :value="permission.id" :checked="permission.checked" color="#ff6b35" />
              </checkbox-group>
              <text class="permission-name">{{ permission.name }}</text>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 底部按钮 -->
      <view class="footer">
        <button class="clear-btn" @click="clearAll">清空</button>
        <button class="confirm-btn" @click="handleConfirm">确定</button>
      </view>
    </view>
  </uni-popup>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const popup = ref();

type SectionKey = 'admin' | 'reception' | 'technician' | 'finance' | 'warehouse';

const expandedSections = ref<Record<SectionKey, boolean>>({
  admin: false,
  reception: false,
  technician: false,
  finance: true,
  warehouse: false
});

interface Permission {
  id: string;
  name: string;
  checked: boolean;
}

interface PermissionSection {
  title: string;
  permissions: Permission[];
}

const permissionData = ref<Record<SectionKey, PermissionSection>>({
  admin: {
    title: '管理员/股东',
    permissions: [
      { id: 'admin_1', name: '查看所有数据', checked: false },
      { id: 'admin_2', name: '编辑系统设置', checked: false },
      { id: 'admin_3', name: '管理用户权限', checked: false }
    ]
  },
  reception: {
    title: '业务接待',
    permissions: [
      { id: 'reception_1', name: '创建工单', checked: false },
      { id: 'reception_2', name: '查看客户信息', checked: false },
      { id: 'reception_3', name: '编辑预约信息', checked: false }
    ]
  },
  technician: {
    title: '技师',
    permissions: [
      { id: 'tech_1', name: '接收工单', checked: false },
      { id: 'tech_2', name: '更新工单状态', checked: false },
      { id: 'tech_3', name: '上传维修照片', checked: false }
    ]
  },
  finance: {
    title: '财务',
    permissions: [
      { id: 'finance_1', name: '查看财务报表', checked: false },
      { id: 'finance_2', name: '处理支付记录', checked: false },
      { id: 'finance_3', name: '导出财务数据', checked: false }
    ]
  },
  warehouse: {
    title: '仓库',
    permissions: [
      { id: 'warehouse_1', name: '管理库存', checked: false },
      { id: 'warehouse_2', name: '处理入库单', checked: false },
      { id: 'warehouse_3', name: '处理出库单', checked: false }
    ]
  }
});

// 切换展开/收起
const toggleSection = (section: SectionKey) => {
  expandedSections.value[section] = !expandedSections.value[section];
};

// 切换单个权限
const togglePermission = (sectionKey: SectionKey, permissionId: string) => {
  const section = permissionData.value[sectionKey];
  const permission = section.permissions.find(p => p.id === permissionId);
  if (permission) {
    permission.checked = !permission.checked;
  }
};

// 清空所有
const clearAll = () => {
  Object.values(permissionData.value).forEach(section => {
    section.permissions.forEach(permission => {
      permission.checked = false;
    });
  });
};

// 确认
const handleConfirm = () => {
  const selectedPermissions = Object.entries(permissionData.value).reduce((acc, [key, section]) => {
    acc[key] = section.permissions.filter(p => p.checked).map(p => p.id);
    return acc;
  }, {} as Record<string, string[]>);
  
  uni.showToast({
    title: '权限已保存',
    icon: 'success'
  });
  popup.value?.close();
};

// 打开弹窗
const open = () => {
  popup.value.open();
};

defineExpose({
  open
});
</script>

<style lang="scss" scoped>
.permission-config {
  width: 580rpx;
  height: 100vh;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  position: relative;
  
  .header {
    height: 88rpx;
    padding: 0 30rpx;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    
    .title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .content {
    flex: 1;
    overflow: hidden;
    padding-bottom: 140rpx;
    
    .section {
      .section-header {
        height: 88rpx;
        padding: 0 30rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #f8f8f8;
        
        .section-title {
          font-size: 28rpx;
          color: #333;
          font-weight: bold;
        }
      }
      
      .permission-list {
        padding: 0 30rpx;
        
        .permission-item {
          height: 88rpx;
          display: flex;
          align-items: center;
          gap: 20rpx;
          border-bottom: 1px solid #eee;
          
          .permission-name {
            font-size: 28rpx;
            color: #333;
          }
        }
      }
    }
  }

  .footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    box-sizing: border-box;
    padding: 20rpx 30rpx;
    background-color: #fff;
    border-top: 1px solid #eee;
    display: flex;
    gap: 20rpx;

    button {
      flex: 1;
      height: 88rpx;
      line-height: 88rpx;
      font-size: 32rpx;
      border-radius: 4rpx;
      border: none;
      margin: 0;
    }

    .clear-btn {
      background-color: #f5f5f5;
      color: #666;
    }

    .confirm-btn {
      background-color: #ff6b35;
      color: #fff;
    }
  }
}
</style> 