<template>
  <view class="team-page">
    <!-- 顶部导航栏 -->
    <uni-nav-bar
      title="团队账户"
      left-icon="left"
      @clickLeft="handleBack"
      :border="false"
      :dark="true"
      status-bar
      fixed
      :background-color="themeColor"
      color="#fff"
    />

    <!-- 提示文本 -->
    <view class="tip-text">向左滑动可以配置对应部门管理权限</view>

    <!-- 角色列表 -->
    <view class="role-list">
      <!-- 管理员/股东 -->
      <uni-swipe-action>
        <uni-swipe-action-item
          :right-options="swipeOptions"
          @click="(e: SwipeEvent) => handleSwipeClick(e, 'admin')"
        >
          <view class="role-section">
            <view class="role-header">
              <text>管理员/股东</text>
              <uni-icons
                type="plus"
                size="20"
                :color="themeColor"
                @click.stop="handleAdd('admin')"
              />
            </view>
            <view class="role-content">
              <view class="tag-list">
                <view
                  class="tag-item"
                  v-for="(item, index) in adminList"
                  :key="index"
                >
                  {{ item.name }}
                </view>
              </view>
            </view>
          </view>
        </uni-swipe-action-item>
      </uni-swipe-action>

      <!-- 业务接待 -->
      <uni-swipe-action>
        <uni-swipe-action-item
          :right-options="swipeOptions"
          @click="(e) => handleSwipeClick(e, 'reception')"
        >
          <view class="role-section">
            <view class="role-header">
              <text>业务接待</text>
              <uni-icons
                type="plus"
                size="20"
                :color="themeColor"
                @click.stop="handleAdd('reception')"
              />
            </view>
            <view class="role-content">
              <view class="tag-list">
                <view
                  class="tag-item"
                  v-for="(item, index) in receptionList"
                  :key="index"
                >
                  {{ item.name }}
                </view>
              </view>
            </view>
          </view>
        </uni-swipe-action-item>
      </uni-swipe-action>

      <!-- 维修技师 -->
      <uni-swipe-action>
        <uni-swipe-action-item
          :right-options="swipeOptions"
          @click="(e) => handleSwipeClick(e, 'technician')"
        >
          <view class="role-section">
            <view class="role-header">
              <text>维修技师</text>
              <uni-icons
                type="plus"
                size="20"
                :color="themeColor"
                @click.stop="handleAdd('technician')"
              />
            </view>
            <view class="role-content">
              <view class="tag-list">
                <view
                  class="tag-item"
                  v-for="(item, index) in technicianList"
                  :key="index"
                >
                  {{ item.name }}
                </view>
              </view>
            </view>
          </view>
        </uni-swipe-action-item>
      </uni-swipe-action>

      <!-- 财务出纳 -->
      <uni-swipe-action>
        <uni-swipe-action-item
          :right-options="swipeOptions"
          @click="(e) => handleSwipeClick(e, 'finance')"
        >
          <view class="role-section">
            <view class="role-header">
              <text>财务出纳</text>
              <uni-icons
                type="plus"
                size="20"
                :color="themeColor"
                @click.stop="handleAdd('finance')"
              />
            </view>
            <view class="role-content">
              <view class="tag-list">
                <view
                  class="tag-item"
                  v-for="(item, index) in financeList"
                  :key="index"
                >
                  {{ item.name }}
                </view>
              </view>
            </view>
          </view>
        </uni-swipe-action-item>
      </uni-swipe-action>

      <!-- 仓储库管 -->
      <uni-swipe-action>
        <uni-swipe-action-item
          :right-options="swipeOptions"
          @click="(e) => handleSwipeClick(e, 'warehouse')"
        >
          <view class="role-section">
            <view class="role-header">
              <text>仓储库管</text>
              <uni-icons
                type="plus"
                size="20"
                :color="themeColor"
                @click.stop="handleAdd('warehouse')"
              />
            </view>
            <view class="role-content">
              <view class="tag-list">
                <view
                  class="tag-item"
                  v-for="(item, index) in warehouseList"
                  :key="index"
                >
                  {{ item.name }}
                </view>
              </view>
            </view>
          </view>
        </uni-swipe-action-item>
      </uni-swipe-action>
    </view>


    <!-- 权限配置弹窗 -->
    <permission-config
      ref="permissionConfigRef"
      @on-submit="handlePermissionSubmit"
    />
  </view>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import PermissionConfig from "./permissionConfig.vue";

// 定义事件类型
interface SwipeEvent {
  index: number;
  content?: any;
}

const themeColor = "var(--primary-color)";
const permissionConfigRef = ref();

// 滑动操作选项
const swipeOptions = ref([
  {
    text: "权限配置",
    style: {
      backgroundColor: themeColor,
      color: "#ffffff",
      width: "160rpx",
    },
  },
]);

// 各角色列表数据
const adminList = ref([
  { name: "刘亚天" },
  { name: "刘亚西" },
  { name: "陈鑫" },
]);

const receptionList = ref([
  { name: "刘亚天" },
  { name: "刘亚西" },
  { name: "陈鑫" },
]);

const technicianList = ref([
  { name: "刘亚天" },
  { name: "刘亚西" },
  { name: "陈鑫" },
]);

const financeList = ref([
  { name: "刘亚天" },
  { name: "刘亚西" },
  { name: "陈鑫" },
]);

const warehouseList = ref([
  { name: "刘亚天" },
  { name: "刘亚西" },
  { name: "陈鑫" },
]);

// 返回上一页
const handleBack = () => {
  uni.navigateBack();
};

// 添加成员
const handleAdd = (role: string) => {
  uni.navigateTo({
    url: "/pages/team/createAccount",
  });
};

// 处理滑动操作点击
const handleSwipeClick = (e: SwipeEvent, role: string) => {
  if (e.index === 0) {
    // 权限配置选项
    permissionConfigRef.value.open(role);
  }
};


// 处理权限配置提交
const handlePermissionSubmit = (data: any) => {
  console.log("权限配置提交:", data);
  uni.showToast({
    title: "配置成功",
    icon: "success",
  });
};
</script>

<style lang="scss" scoped>
$theme-color: var(--primary-color);

.team-page {
  // min-height: 100vh;
  background-color: #f5f5f5;
  // padding-top: 176rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
  padding: 20rpx 30rpx;
}

.role-section {
  margin-bottom: 20rpx;
  background-color: #fff;

  .role-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 88rpx;
    padding: 0 30rpx;
    font-size: 28rpx;
    color: #333;

    :deep(.uni-icons) {
      color: $theme-color !important;
    }
  }

  .role-content {
    padding: 0 30rpx 20rpx;

    .tag-list {
      display: flex;
      flex-wrap: wrap;
      margin: 0 -10rpx;

      .tag-item {
        height: 60rpx;
        line-height: 60rpx;
        padding: 0 30rpx;
        background-color: #f5f5f5;
        border-radius: 4rpx;
        font-size: 28rpx;
        color: #333;
        margin: 10rpx;
      }
    }
  }
}

.bottom-button {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx 30rpx;
  background-color: #fff;

  .config-btn {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    background-color: $theme-color;
    color: #fff;
    font-size: 32rpx;
    border-radius: 0;
    text-align: center;
  }
}

:deep(.uni-nav-bar) {
  .uni-nav-bar__header {
    background-color: $theme-color !important;
  }
  .uni-nav-bar__text {
    color: #fff !important;
  }
}

:deep(.uni-swipe-action) {
  margin-bottom: 20rpx;

  .uni-swipe-action__content {
    background-color: #fff;
  }

  .role-section {
    margin-bottom: 0;
  }
}
</style>
