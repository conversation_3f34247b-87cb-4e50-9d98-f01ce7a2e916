<template>
  <scroll-view class="create-account">
    <!-- 顶部导航栏 -->
    <uni-nav-bar
      title="创建账户"
      left-icon="left"
      @clickLeft="handleBack"
      :border="false"
      :dark="true"
      status-bar
      fixed
      :background-color="themeColor"
      color="#fff"
    />
    
    <view class="form-content">
      <!-- 头像上传 -->
      <view class="avatar-section">
        <view class="avatar-box" @click="chooseImage">
          <image v-if="avatar" :src="avatar" mode="aspectFill" class="avatar-image"></image>
          <uni-icons v-else type="camera-filled" size="40" color="#fff"></uni-icons>
        </view>
        <text class="upload-text">头像上传</text>
      </view>

      <!-- 表单部分 -->
      <view class="form-section">
        <!-- 姓名 -->
        <uni-forms-item>
          <view class="form-item">
            <text class="label">姓名:</text>
            <input class="input" v-model="formData.name" placeholder="请输入姓名" />
          </view>
        </uni-forms-item>

        <!-- 性别 -->
        <uni-forms-item>
          <view class="form-item">
            <text class="label">性别:</text>
            <view class="input-right" @click="showGenderPicker">
              <text :class="{'placeholder': !formData.gender}">{{formData.gender || '男'}}</text>
              <uni-icons type="right" size="16" color="#666"></uni-icons>
            </view>
          </view>
        </uni-forms-item>

        <!-- 手机号码 -->
        <uni-forms-item>
          <view class="form-item">
            <text class="label">手机号码:</text>
            <input class="input" v-model="formData.phone" placeholder="默认登录密码为手机号后6位" />
          </view>
        </uni-forms-item>

        <!-- 所属部门 -->
        <uni-forms-item>
          <view class="form-item">
            <text class="label">所属部门:</text>
            <view class="input-right" @click="showDepartmentPicker">
              <text :class="{'placeholder': !formData.department}">{{formData.department || '维修技师'}}</text>
              <uni-icons type="right" size="16" color="#666"></uni-icons>
            </view>
          </view>
        </uni-forms-item>

        <!-- 权限配置 -->
        <uni-forms-item>
          <view class="form-item">
            <text class="label">权限配置:</text>
            <view class="input-right" @click="showPermissionConfig">
              <text class="permission-text">点击配置</text>
              <uni-icons type="right" size="16" color="#666"></uni-icons>
            </view>
          </view>
        </uni-forms-item>
      </view>

      <!-- 身份证照片上传 -->
      <view class="id-card-section">
        <view class="section-title">身份证照片</view>
        <view class="upload-area">
          <!-- 正面 -->
          <view class="upload-item">
            <view class="upload-box" @click="chooseIdCardFront">
              <image v-if="formData.idCardFront" :src="formData.idCardFront" mode="aspectFill" class="id-card-image"></image>
              <template v-else>
                <uni-icons type="camera" size="30" color="#999"></uni-icons>
                <text class="upload-tip">身份证正面</text>
              </template>
            </view>
          </view>
          <!-- 反面 -->
          <view class="upload-item">
            <view class="upload-box" @click="chooseIdCardBack">
              <image v-if="formData.idCardBack" :src="formData.idCardBack" mode="aspectFill" class="id-card-image"></image>
              <template v-else>
                <uni-icons type="camera" size="30" color="#999"></uni-icons>
                <text class="upload-tip">身份证反面</text>
              </template>
            </view>
          </view>
        </view>
      </view>

      <!-- 备注说明 -->
      <view class="remark-section">
        <text class="section-title">备注说明</text>
        <textarea class="remark-input" v-model="formData.remark" placeholder="请输入备注说明"></textarea>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-button">
      <button class="submit-btn" @click="handleSubmit">确定添加</button>
    </view>

    <!-- 性别选择器 -->
    <uni-popup ref="genderPopup" type="bottom">
      <uni-list>
        <uni-list-item title="男" @click="selectGender('男')" />
        <uni-list-item title="女" @click="selectGender('女')" />
      </uni-list>
    </uni-popup>

    <!-- 部门选择器 -->
    <uni-popup ref="departmentPopup" type="bottom">
      <uni-list>
        <uni-list-item title="维修技师" @click="selectDepartment('维修技师')" />
        <uni-list-item title="业务接待" @click="selectDepartment('业务接待')" />
        <uni-list-item title="财务出纳" @click="selectDepartment('财务出纳')" />
        <uni-list-item title="仓储库管" @click="selectDepartment('仓储库管')" />
      </uni-list>
    </uni-popup>

    <!-- 权限配置弹窗 -->
    <permission-config ref="permissionConfigRef" />
  </scroll-view>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import PermissionConfig from './permissionConfig.vue';

const themeColor = 'var(--primary-color)';

// 表单数据
const formData = ref({
  name: '',
  gender: '男',
  phone: '',
  department: '维修技师',
  idCardFront: '',
  idCardBack: '',
  remark: ''
});

// 头像
const avatar = ref('');

// popup引用
const genderPopup = ref();
const departmentPopup = ref();
const permissionConfigRef = ref();

// 返回上一页
const handleBack = () => {
  uni.navigateBack();
};

// 选择头像
const chooseImage = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      avatar.value = res.tempFilePaths[0];
    }
  });
};

// 选择身份证照片
const chooseIdCardFront = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      formData.value.idCardFront = res.tempFilePaths[0];
    }
  });
};

const chooseIdCardBack = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      formData.value.idCardBack = res.tempFilePaths[0];
    }
  });
};

// 显示性别选择器
const showGenderPicker = () => {
  genderPopup.value.open();
};

// 选择性别
const selectGender = (gender: string) => {
  formData.value.gender = gender;
  genderPopup.value.close();
};

// 显示部门选择器
const showDepartmentPicker = () => {
  departmentPopup.value.open();
};

// 选择部门
const selectDepartment = (department: string) => {
  formData.value.department = department;
  departmentPopup.value.close();
};

// 显示权限配置
const showPermissionConfig = () => {
  permissionConfigRef.value?.open();
};

// 提交表单
const handleSubmit = () => {
  console.log('提交表单', formData.value);
  // TODO: 实现表单提交逻辑
};
</script>

<style lang="scss" scoped>
$theme-color: var(--primary-color);

.create-account {
  min-height: 100%;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.form-content {
  padding-top: 20rpx;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
  background-color: #fff;

  .avatar-box {
    width: 160rpx;
    height: 160rpx;
    border-radius: 50%;
    background-color: $theme-color;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20rpx;
    overflow: hidden;
  }

  .avatar-image {
    width: 100%;
    height: 100%;
  }

  .upload-text {
    font-size: 28rpx;
    color: #333;
  }
}

.form-section {
  margin-top: 20rpx;
  background-color: #fff;

  :deep(.uni-forms-item) {
    margin-bottom: 0;
    padding: 0;
    
    .uni-forms-item__inner {
      padding: 0;
    }
  }

  .form-item {
    display: flex;
    align-items: center;
    height: 98rpx;
    padding: 0 30rpx;
    border-bottom: 1rpx solid #eee;

    .label {
      width: 160rpx;
      font-size: 28rpx;
      color: #333;
      line-height: 1;
    }

    .input {
      flex: 1;
      height: 40rpx;
      font-size: 28rpx;
      line-height: 40rpx;
    }

    .input-right {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      
      .permission-text {
        font-size: 28rpx;
        color: $theme-color;
      }
    }

    .placeholder {
      color: #999;
    }
  }
}

.id-card-section {
  margin-top: 20rpx;
  margin-bottom: 30rpx;
  background-color: #fff;

  .section-title {
    font-size: 28rpx;
    color: #333;
    padding: 30rpx;
  }

  .upload-area {
    display: flex;
    justify-content: space-between;
    padding: 0 30rpx 30rpx 30rpx;

    .upload-item {
      width: 320rpx;

      .upload-box {
        width: 100%;
        height: 200rpx;
        background-color: #f5f5f5;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-radius: 8rpx;
      }

      .id-card-image {
        width: 100%;
        height: 100%;
      }

      .upload-tip {
        font-size: 24rpx;
        color: #999;
        margin-top: 10rpx;
      }
    }
  }
}

.remark-section {
  background-color: #fff;
  padding: 30rpx;

  .section-title {
    font-size: 28rpx;
    color: #333;
  }

  .remark-input {
    width: 100%;
    height: 240rpx;
    background-color: #f5f5f5;
    padding: 20rpx;
    font-size: 28rpx;
    border-radius: 8rpx;
    margin-top: 20rpx;
    box-sizing: border-box;
  }
}

.bottom-button {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx 30rpx;
  background-color: #fff;

  .submit-btn {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    background-color: $theme-color;
    color: #fff;
    font-size: 32rpx;
    border-radius: 0;
    text-align: center;
  }
}
</style> 