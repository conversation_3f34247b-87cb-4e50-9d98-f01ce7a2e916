<template>
  <view class="edit-page">
    <!-- 顶部导航 -->
    <uni-nav-bar
      :border="false"
      title="编辑服务"
      left-icon="left"
      @clickLeft="onBack"
      background-color="#1989fa"
      color="#fff"
      status-bar
    />

    <!-- 表单内容 -->
    <view class="form-content">
      <view class="form-item">
        <text class="label">服务名称：</text>
        <input
          class="input"
          v-model="formData.title"
          placeholder="请输入服务名称"
          placeholder-class="placeholder"
        />
        <text class="required">*</text>
      </view>

      <view class="form-item">
        <text class="label">收费价格：</text>
        <input
          class="input"
          v-model="formData.price"
          type="digit"
          placeholder="请输入价格"
          placeholder-class="placeholder"
        />
        <text class="unit">元</text>
        <text class="required">*</text>
      </view>

      <view class="form-item">
        <text class="label">服务工时：</text>
        <input
          class="input"
          v-model="formData.hours"
          type="number"
          placeholder="请输入服务工时数量"
          placeholder-class="placeholder"
        />
        <text class="unit">个</text>
      </view>

      <view class="form-item">
        <text class="label">工时单价：</text>
        <input
          class="input"
          v-model="formData.priceunit"
          type="digit"
          placeholder="请输入工时单价"
          placeholder-class="placeholder"
        />
        <text class="unit">元</text>
      </view>

      <!-- 功能说明 -->
      <view class="tips-section">
        <view class="tips-title">功能说明：</view>
        <view class="tips-content">
          <view class="tip-item"
            >1、服务收费价格主要面向车主维修工单展示。</view
          >
          <view class="tip-item"
            >2、服务工时是便于计算维修工作量，例如：李师傅为车主提供一次常规小保养服务，工时2，工时单价8元，那么李师傅做这个小保养的收益是16元。</view
          >
          <view class="tip-item"
            >3、为李师傅创建员工账户，当维修工作指派给李师傅后，李师傅就可以在维修工单中看到工时奖励。</view
          >
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <button v-if="isEdit" class="btn btn-cancel" @tap="onDelete">
        删 除
      </button>
      <button class="btn btn-submit" @tap="onSubmit">保 存</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import {
  getServiceDetail,
  addService,
  editService,
  deleteService,
} from "@/api/service";
import type { ApiResponse } from "@/utils/type";
// 是否是编辑模式
const isEdit = ref(false);
const editId = ref("");
interface FormData {
  id: string;
  title: string; // 服务名称
  price: number | undefined; // 收费价格（单位元）
  priceunit: number | undefined; // 工时单价（单位元）
  hours: number | undefined; // 工时计数
}

// 表单数据
const formData = ref<FormData>({
  id: "",
  title: "",
  price: undefined,
  priceunit: undefined,
  hours: undefined,
});

// 检查必填字段
const validateForm = () => {
  if (!formData.value.title) {
    uni.showToast({
      title: "请输入服务名称",
      icon: "none",
    });
    return false;
  }
  if (!formData.value.price) {
    uni.showToast({
      title: "请输入收费价格",
      icon: "none",
    });
    return false;
  }
  return true;
};

// 返回上一页
const onBack = () => {
  uni.navigateBack();
};

// 删除服务
const onDelete = () => {
  if (!isEdit.value) return;

  uni.showModal({
    title: "提示",
    content: "确定要删除该服务吗？",
    success: async (res) => {
      if (res.confirm) {
        try {
          await deleteService({ id: editId.value });
          uni.showToast({
            title: "删除成功",
            icon: "success",
            duration: 2000,
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 2000);
        } catch (error) {
          uni.showToast({
            title: "删除失败",
            icon: "error",
          });
        }
      }
    },
  });
};

// 提交表单
const onSubmit = async () => {
  if (!validateForm()) return;

  try {
    const submitData = {
      ...formData.value,
      price: Number(formData.value.price),
      priceunit: Number(formData.value.priceunit),
      hours: Number(formData.value.hours),
    };

    const submitFn = isEdit.value ? editService : addService;

    const res: ApiResponse = (await submitFn(submitData)) as ApiResponse;

    if (res.code === 200) {
      uni.showToast({
        title: "保存成功",
        icon: "success",
      });
      uni.navigateBack();
    }
  } catch (error) {
    uni.showToast({
      title: "保存失败",
      icon: "error",
    });
  }
};

// 获取编辑数据
const getEditData = async () => {
  const id = uni.getStorageSync("editServiceId");
  if (id) {
    editId.value = id;
    isEdit.value = true;
    try {
      const res: ApiResponse = (await getServiceDetail({ id })) as ApiResponse;
      if (res.data) {
        formData.value = {
          id: res.data.id,
          title: res.data.title,
          price: res.data.price,
          priceunit: res.data.priceunit,
          hours: res.data.hours,
        };
      }
    } catch (error) {
      uni.showToast({
        title: "获取数据失败",
        icon: "error",
      });
    }
  }
};

onMounted(() => {
  getEditData();
});
</script>

<style scoped>
.edit-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

.form-content {
  background: #fff;
  padding: 0 30rpx;
}

.form-item {
  display: flex;
  align-items: center;
  min-height: 100rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.label {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
}

.input {
  flex: 1;
  height: 100rpx;
  font-size: 28rpx;
}

.placeholder {
  color: #999;
}

.unit {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: #666;
}

.required {
  color: #ff4d4f;
  margin-left: 8rpx;
}

.tips-section {
  padding: 30rpx 0;
}

.tips-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.tips-content {
  color: #666;
  font-size: 26rpx;
  line-height: 1.6;
}

.tip-item {
  margin-bottom: 16rpx;
}

.footer {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  padding: 20rpx;
  background: #fff;
  box-shadow: 0 -2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border-radius: 4rpx;
}

.btn-cancel {
  margin-right: 20rpx;
  background: #fff;
  color: #666;
  border: 2rpx solid #ddd;
}

.btn-submit {
  background: #1989fa;
  color: #fff;
}

/* 导航栏样式 */
:deep(.uni-nav-bar__content) {
  background-color: #1989fa !important;
}

:deep(.uni-nav-bar__header) {
  background-color: #1989fa !important;
}

:deep(.uni-nav-bar-text) {
  color: #fff !important;
}

:deep(.uni-icons) {
  color: #fff !important;
}
</style>
