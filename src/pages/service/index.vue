<template>
  <view class="service-page">
    <uni-nav-bar
      :border="false"
      title="服务管理"
      right-text="新增"
      left-icon="left"
      @clickRight="onAdd"
      @clickLeft="onBack"
      background-color="#1989fa"
      color="#fff"
      status-bar
      fixed
    />
    <view class="search-bar">
      <uni-easyinput
        v-model="searchValue"
        placeholder="搜索关键词或首字母/单位"
        suffixIcon="search"
        @iconClick="onSearch"
      />
    </view>
    <uv-list>
      <uv-list-item v-for="item in serviceList" :key="item.id">
        <view class="service-item">
          <view class="item-main">
            <view class="item-name">{{ item.title }}</view>
            <view class="item-price" :class="{ red: !item.price }">
              {{ item.price ? "￥" + item.price + " 元" : "请定价" }}
            </view>
          </view>
          <button class="edit-btn" @click="onEdit(item)">修改</button>
        </view>
      </uv-list-item>
      <uv-load-more :status="status" />
    </uv-list>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { getLocalServiceList } from "@/api/service";
import { onReachBottom } from "@dcloudio/uni-app";

const searchValue = ref("");
const serviceList = ref<any[]>([]);
const status = ref("loadmore");
const page = ref(1);
const limit = ref(20);
const total = ref(0);
// 获取服务列表
const fetchList = async () => {
  status.value = "loading";
  const params = {
    name: searchValue.value,
    page: page.value,
    limit: limit.value,
  };

  const res: any = await getLocalServiceList(params);
  if (res.code === 200) {
    if (page.value === 1) {
      serviceList.value = res.data?.list || [];
    } else {
      serviceList.value = [...serviceList.value, ...res.data?.list];
    }
    total.value = res.data?.count || 0;
    status.value = "loadmore";
  }
};
const onBack = () => {
  uni.navigateBack();
};
const onSearch = () => {
  page.value = 1;
  fetchList();
};
const onAdd = () => {
  uni.setStorageSync("editServiceId", "");
  uni.navigateTo({ url: "/pages/service/edit" });
};
const onEdit = (item: any) => {
  uni.setStorageSync("editServiceId", item.id);
  uni.navigateTo({ url: "/pages/service/edit" });
};

const pageTotal = computed(() => {
  return Math.ceil(total.value / limit.value);
});

onMounted(() => {
  fetchList();
});
onReachBottom(() => {
  if (page.value > pageTotal.value) {
    status.value = "no-more";
    return;
  }
  if (status.value === "loadmore") {
    page.value++;
    fetchList();
  }
});
</script>

<style scoped lang="scss">
.service-page {
  min-height: 100vh;
  background: #f5f5f5;
  position: relative;
}
.search-bar {
  padding: 20rpx;
  background: #fff;
}
.service-list {
  height: 80vh;
  overflow-y: scroll;
  background: #fff;
}
.group-title {
  padding: 16rpx 24rpx;
  background: #f0f0f0;
  color: #1989fa;
  font-weight: bold;
}
.service-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18rpx 24rpx;
  border-bottom: 1px solid #f5f5f5;
  .item-main {
    flex: 1;
    .item-name {
      font-size: 30rpx;
      color: #333;
    }
    .item-price {
      font-size: 26rpx;
      color: #ff6b00;
      margin-top: 6rpx;
    }
    .item-price.red {
      color: #ff4d4f;
    }
  }
  .edit-btn {
    background: #1989fa;
    color: #fff;
    border-radius: 8rpx;
    font-size: 26rpx;
    padding: 8rpx 24rpx;
    margin-left: 20rpx;
  }
}
.letter-index {
  position: fixed;
  right: 10rpx;
  top: 200rpx;
  display: flex;
  flex-direction: column;
  .letter {
    color: #1989fa;
    font-size: 24rpx;
    padding: 4rpx 0;
    text-align: center;
    cursor: pointer;
  }
}
</style>
