<template>
  <uni-nav-bar
    :border="false"
    title="库存管理"
    color="#fff"
    left-icon="left"
    right-icon="add"
    @clickLeft="handleClickLeft"
    @clickRight="handleClickRight"
    backgroundColor="var(--primary-color)"
  ></uni-nav-bar>
  <view class="inventory-manage">
    <view class="inventory-top">
      <view class="inventory-count">
        <view class="inventory-count-item">
          <view class="item-num">100</view>
          <view class="item-text">库房种类</view>
        </view>
        <view class="inventory-count-item">
          <view class="item-num">100</view>
          <view class="item-text">库房总量</view>
        </view>
        <view class="inventory-count-item">
          <view class="item-num">100</view>
          <view class="item-text">库房预警</view>
        </view>
      </view>
      <uni-easyinput
        class="search-input"
        suffixIcon="search"
        v-model="value"
        placeholder="搜索库房产品"
        @iconClick="iconClick"
      ></uni-easyinput>
    </view>
    <uni-swipe-action>
      <!-- 使用插槽 （请自行给定插槽内容宽度）-->
      <uni-swipe-action-item>
        <view class="inventory-list-item">
          <view class="item-row flex-sbw">
            <view class="item-row-name">火花塞（布雷博）*4</view>
            <view>库位 ：1号仓库1号货架</view>
          </view>
          <view class="item-row flex-sbw">
            <view
              >入库单价：<text class="color1"
                >66.00
                <text style="font-size: 2.4vw; margin-left: 0.5vw"
                  >元</text
                ></text
              ></view
            >
            <view>
              售价：<text class="color2"
                >96.00
                <text style="font-size: 2.4vw; margin-left: 0.5vw"
                  >元</text
                ></text
              >
            </view>
            <view>
              库存 ：<text class="color3"
                >0 <text class="status-text">个</text></text
              >
            </view>
          </view>
        </view>
        <template #right>
          <view class="inventory-edit" @touchend="handleClickAdd">
            <image
              src="@/static/images/warehouse/inventory_edit.png"
              mode="aspectFit"
            />
            修&nbsp;改</view
          >
        </template>
      </uni-swipe-action-item>
    </uni-swipe-action>
    <uni-popup ref="popup" type="bottom" :mask-click="false">
      <view class="popup-content">
        <view class="popup-header flex-center"
          >配件编辑<uni-icons
            class="close-icon"
            type="close"
            color="#E0E0E0"
            size="24"
            @click="handleClickClose"
          ></uni-icons
        ></view>
        <uni-forms
          label-width="24vw"
          class="popup-form"
          ref="baseForm"
          :modelValue="baseFormData"
        >
          <uni-forms-item label="配件名称">
            <uni-easyinput
              v-model="baseFormData.name"
              :clearable="false"
              placeholder="请输入配件名称"
            />
          </uni-forms-item>
          <uni-forms-item label="产品单位">
            <uni-easyinput
              v-model="baseFormData.unit"
              :clearable="false"
              placeholder="请输入产品单位"
            />
          </uni-forms-item>
          <uni-forms-item label="采购价格">
            <uni-easyinput
              v-model="baseFormData.purchasePrice"
              :clearable="false"
              type="number"
              placeholder="请输入采购价格"
            />
          </uni-forms-item>
          <uni-forms-item label="是否入库">
            <image
              class="vip-icon"
              src="@/static/images/warehouse/vip.png"
              mode="aspectFit"
            />
            <switch
              :checked="baseFormData.isInStock"
              disabled
              style="transform: scale(0.7)"
              color="var(--primary-color)"
            />
          </uni-forms-item>
          <uni-forms-item label="入库数量">
            <uni-easyinput
              type="number"
              v-model="baseFormData.inStockNum"
              :clearable="false"
              placeholder="请输入入库数量"
            />
          </uni-forms-item>
          <uni-forms-item label="入库库位">
            <uni-data-select
              v-model="baseFormData.position"
              placeholder="请选择入库库位"
              :clear="false"
              :localdata="range"
            ></uni-data-select>
          </uni-forms-item>
          <uni-forms-item label="供应商">
            <uni-data-select
              v-model="baseFormData.supplier"
              placeholder="请选择供应商"
              :clear="false"
              :localdata="supplierRange"
            ></uni-data-select>
          </uni-forms-item>
          <button type="primary" @click="submit">保 存</button>
        </uni-forms>
      </view>
    </uni-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import {
  addAccessoryProduct,
  editAccessoryProduct,
  getLocalAccessoryList,
  getVehicleAccessoryDetail,
} from "@/api/accessory";
import { getWarehouseList, getSupplierList } from "@/api/warehouse";
import type { UniPopup } from "uni-ui-types";

const handleClickLeft = () => {
  uni.navigateBack();
};
const value = ref("");
const isEdit = ref(false);
const editId = ref("");
const baseFormData = reactive({
  name: "",
  unit: "",
  purchasePrice: "",
  isInStock: false,
  inStockNum: "",
  position: "",
  supplier: "",
});
const range = ref([
  {
    value: "1",
    text: "1号仓库1号货架",
  },
  {
    value: "2",
    text: "2号仓库2号货架",
  },
]);
const supplierRange = ref([
  {
    value: "1",
    text: "供应商1",
  },
  {
    value: "2",
    text: "供应商2",
  },
]);
const popup = ref<UniPopup>();

// 配件列表
const accessoryList = ref<any[]>([]);

// 获取配件列表
const fetchAccessoryList = async (search = "") => {
  const res: any = await getLocalAccessoryList({ name: search });
  if (res.code === 200) {
    accessoryList.value = res.data.list || [];
  }
};

// 搜索
const iconClick = () => {
  fetchAccessoryList(value.value);
};

// 新增/编辑弹窗
const handleClickAdd = async (item?: any) => {
  if (item && item.id) {
    // 编辑
    isEdit.value = true;
    editId.value = item.id;
    const res: any = await getVehicleAccessoryDetail({ id: item.id });
    Object.assign(baseFormData, res.data || {});
  } else {
    // 新增
    isEdit.value = false;
    editId.value = "";
    Object.assign(baseFormData, {
      name: "",
      unit: "",
      purchasePrice: "",
      isInStock: false,
      inStockNum: "",
      position: "",
      supplier: "",
    });
  }
  popup.value?.open();
};

const handleClickEdit = (item: any) => {
  handleClickAdd(item);
};

const submit = async () => {
  try {
    if (isEdit.value) {
      await editAccessoryProduct({ ...baseFormData, id: editId.value });
      uni.showToast({ title: "编辑成功", icon: "success" });
    } else {
      await addAccessoryProduct({ ...baseFormData });
      uni.showToast({ title: "新增成功", icon: "success" });
    }
    popup.value?.close();
    fetchAccessoryList();
  } catch (e) {
    uni.showToast({ title: "操作失败", icon: "none" });
  }
};

const handleClickClose = () => {
  popup.value?.close();
};
const handleClickDelete = (e: any) => {
  console.log("handleClickDelete", e);
};
const handleClickRight = () => {
  handleClickAdd();
};

// 获取仓库列表
const warehouseList = ref<any[]>([]);
const fetchWarehouseList = async () => {
  const res: any = await getWarehouseList({});
  if (res.code === 200) {
    warehouseList.value = res.data.list.map((item: any) => ({
      value: item.id,
      text: item.name,
    }));
  }
};

// 获取供应商列表
const supplierList = ref<any[]>([]);
const fetchSupplierList = async () => {
  const res: any = await getSupplierList({});
  if (res.code === 200) {
    supplierList.value = res.data.list.map((item: any) => ({
      value: item.id,
      text: item.supplier_name,
    }));
  }
};

onMounted(() => {
  fetchAccessoryList();
  fetchWarehouseList();
  fetchSupplierList();
});
</script>

<style scoped lang="less">
.color1 {
  font-size: 3.47vw;
  color: var(--warning-color);
}
.color2 {
  color: var(--primary-color);
  font-size: 3.47vw;
}
.color3 {
  color: var(--danger-color);
  color: var(--text-color);
  font-size: 3.47vw;
  .status-text {
    font-size: 2.4vw;
    margin-left: 0.5vw;
  }
}

.inventory-manage {
  .inventory-top {
    padding: 3.5vw;
    .search-input {
      margin-top: 4.8vw;
    }
  }
  .inventory-count {
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: 27.6vw;
    box-shadow: 0 0.33vw 0.5vw 0.5vw #e8e2e2;
    border-radius: 0.56vw;
    box-sizing: border-box;
    .inventory-count-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .item-num {
        font-size: 4.8vw;
        font-weight: bold;
        margin-bottom: 3.8vw;
        color: #ee840e;
      }
      .item-text {
        font-size: 3.73vw;
        color: #515151;
      }
    }
  }
  .inventory-list-item {
    padding: 0 3.5vw;
    // margin-bottom: 4.8vw;
    .item-row {
      font-size: 3.2vw;
      color: #5f5f5f;
      height: 10vw;

      .item-row-name {
        color: var(--text-color);
        font-size: 3.73vw;
      }
    }
  }
  .inventory-edit {
    width: 35vw;
    height: 100%;
    background-color: var(--warning-color);
    color: #fff;
    font-size: 4.8vw;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    position: relative;
    z-index: 9;
    image {
      width: 5.6vw;
      height: 5.6vw;
      margin-right: 2.67vw;
    }
  }
  .popup-content {
    width: 100%;
    height: 142vw;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 3.33vw 3.33vw 0 0;
    .popup-header {
      height: 12.67vw;
      font-size: 4.53vw;
      color: #515151;
      position: relative;
      border-bottom: 0.27vw solid #e0e0e0;
      .close-icon {
        width: 4.2vw;
        height: 4.2vw;
        position: absolute;
        right: 4.8vw;
        top: 3.5vw;
      }
    }
    .popup-form {
      padding: 3.5vw;

      .vip-icon {
        position: absolute;
        width: 5.07vw;
        height: 2.67vw;
        left: -8vw;
        top: 1.8vw;
      }
    }
  }
}
</style>
