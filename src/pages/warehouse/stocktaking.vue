<template>
  <uni-nav-bar
    :border="false"
    title="新建盘库"
    color="#fff"
    left-icon="left"
    @clickLeft="handleClickLeft"
    fixed
    statusBar
    backgroundColor="var(--primary-color)"
  ></uni-nav-bar>
  <view class="stocktaking-manage">
    <view class="stocktaking-item">
      <view class="stocktaking-item-row flex-sbw">
        <view class="item-name">嘉多实机油0wW20</view>
        <view>库存量：3件</view>
      </view>
      <view class="stocktaking-item-row flex-sbw">
        <view>1号仓库/1号货架</view>
        <view class="stocktaking-num"
          >实际量：<uni-easyinput
            :clearable="false"
            type="number"
            v-model="value"
          ></uni-easyinput
        ></view>
      </view>
      <view class="stocktaking-foot flex-sbw">
        <view class="status">正常</view>
        <view class="foot-btn flex-center">确&nbsp;&nbsp;定</view>
      </view>
    </view>
    <view class="stocktaking-add">
      <view class="warehouse-add-text"> 提交记录 </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue"
import {
  getInventoryDetail,
  addInventory,
  editInventory,
} from "@/api/inventory"
import { Response } from "@/utils/type"
const handleClickLeft = () => {
  uni.navigateBack()
}
const getInventoryDetailData = async () => {
  const res: Response = (await getInventoryDetail({})) as Response
  if (res.code === 200) {
    console.log(res.data)
  }
}
const value = ref("")
const addInventoryData = async () => {
  const res: Response = (await addInventory({})) as Response
  if (res.code === 200) {
    console.log(res.data)
  }
}
</script>

<style scoped lang="scss">
.stocktaking-manage {
  padding: 3.5vw;

  .stocktaking-item {
    box-shadow: 0 0.33vw 0.5vw 0.5vw #e8e2e2;
    border-radius: 0.56vw;
    padding: 1.7vw;
    margin-bottom: 3.5vw;

    .stocktaking-item-row {
      font-size: 4.8vw;
      color: #515151;

      .item-name {
        width: 55vw;
        text-overflow: ellipsis;
        text-align: center;
        overflow: hidden;
        white-space: nowrap;
        line-height: 9.87vw;
        border-radius: 0.67vw;
        border: 0.27vw solid #7a7a7a;
      }
      .stocktaking-num {
        display: flex;
        align-items: center;
        padding: 4.8vw 0;
        .uni-easyinput {
          width: 20.67vw;
        }
      }
    }

    .stocktaking-foot {
      border-top: 0.27vw solid #e6e6e6;
      padding: 3.5vw 5.3vw;
      .status {
        color: var(--success-color);
        font-size: 4.8vw;
        &.status1 {
          color: var(--danger-color);
        }
        &.status2 {
          color: var(--primary-color);
        }
      }
      .foot-btn {
        width: 19.36vw;
        height: 9.6vw;
        border-radius: 0.67vw;
        background: var(--warning-color);
        color: #fff;
        &.status1 {
          background: var(--info-color);
        }
      }
    }
  }

  .stocktaking-add {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 15.33vw;
    background: var(--primary-color);
    border-radius: 1.33vw 1.33vw 0vw 0vw;
    text-align: center;
    line-height: 15.33vw;
    color: #fff;
    font-size: 4.8vw;
  }
}
</style>
