<template>
  <uni-nav-bar
    :border="false"
    title="库房盘点"
    color="#fff"
    left-icon="left"
    @clickLeft="handleClickLeft"
    backgroundColor="var(--primary-color)"
  ></uni-nav-bar>
  <view class="warehouse-manage">
    <view
      class="warehouse-manage-item"
      v-for="item in warehouseList"
      :key="item.id"
    >
      <view class="warehouse-item-title">
        <view class="row-main flex-sbw">
          <view class="row-title">盘库时间：{{ item.create_time }}</view>
          <image
            src="@/static/images/warehouse/stocktaking.png"
            mode="aspectFit"
          />
        </view>
      </view>
      <view class="warehouse-item-content">
        <view class="warehouse-content-item">
          <view class="warehouse-item-num">
            {{ item.product_stock_count }}
          </view>
          <view class="warehouse-item-text"> 库房总量 </view>
        </view>
        <view class="warehouse-content-item">
          <view class="warehouse-item-num"> {{ item.gain_num }} </view>
          <view class="warehouse-item-text"> 盘盈数量 </view>
        </view>
        <view class="warehouse-content-item">
          <view class="warehouse-item-num"> {{ item.loss_num }} </view>
          <view class="warehouse-item-text"> 盘亏数量 </view>
        </view>
      </view>
    </view>
    <view class="warehouse-add" @click="handleClickAdd"> 新建盘库 </view>
  </view>
</template>

<script setup lang="ts">
import { getInventoryList } from "@/api/inventory";
import { Response } from "@/utils/type";
import { ref, onMounted } from "vue";
const handleClickLeft = () => {
  uni.navigateBack();
};
interface Warehouse {
  id: number; // 库存盘点明细ID
  product_stock_count: number; // 盘点库存明细种类数量
  gain_num: number; // 盘盈数量合计
  loss_num: number; // 盘亏数量
  remark: string; // 库存盘点备注
  status: number; // 库存盘点单据状态：0-待盘点，1-已完成，2-已作废
  create_time: string; // 盘库时间
}
const warehouseList = ref<Warehouse[]>([]);
const handleClickAdd = () => {
  uni.navigateTo({
    url: "/pages/warehouse/stocktaking",
  });
};
const getWarehouseData = async () => {
  const res: Response = (await getInventoryList({})) as Response;
  if (res.code === 200) {
    warehouseList.value = res.data.list;
  }
};
onMounted(() => {
  getWarehouseData();
});
</script>

<style scoped lang="scss">
.warehouse-manage {
  padding: 3.5vw;
  .warehouse-add {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 15.33vw;
    background: var(--primary-color);
    border-radius: 1.33vw 1.33vw 0vw 0vw;
    text-align: center;
    line-height: 15.33vw;
    color: #fff;
    font-size: 4.8vw;
  }

  .warehouse-manage-item {
    padding: 3.5vw;
    background-color: #fff;
    border-radius: 0.56vw;
    box-shadow: 0 0.33vw 0.5vw 0.5vw #e8e2e2;
    color: #333333;
    font-size: 3.73vw;
    margin-bottom: 3.5vw;

    .warehouse-item-title {
      height: 8.4vw;
      display: flex;
      align-items: center;
      .row-main {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        image {
          width: 3.73vw;
          height: 3.73vw;
          margin-left: 1.33vw;
        }
      }
    }
    .warehouse-item-content {
      padding-top: 2.67vw;
      display: flex;
      align-items: center;
      justify-content: space-around;
      .warehouse-content-item {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        &:nth-child(2) {
          .warehouse-item-num {
            color: var(--warning-color);
          }
        }
        &:nth-child(3) {
          .warehouse-item-num {
            color: var(--success-color);
            &.status1 {
              color: var(--danger-color);
            }
            &.status2 {
              color: var(--primary-color);
            }
          }
        }
        .warehouse-item-num {
          font-size: 4.8vw;
          color: var(--primary-color);
        }
        .warehouse-item-text {
          padding-top: 3.33vw;
          font-size: 3.47vw;
          color: #333333;
        }
      }
    }
  }
}
</style>
