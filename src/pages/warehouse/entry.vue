<template>
  <uni-nav-bar
    :border="false"
    title="采购入库"
    color="#fff"
    left-icon="left"
    @clickLeft="handleClickLeft"
    backgroundColor="var(--primary-color)"
  ></uni-nav-bar>
  <view class="entry-list">
    <view class="entry-list-item">
      <view class="flex-sbw entry-item-row">
        <view class="row-item">
          <text>订单编号：</text>
          <text>1234567890</text>
        </view>
        <view class="row-item">
          <text>入库人员：</text>
          <text class="color1">刘小明</text>
        </view>
      </view>
      <view class="flex-sbw entry-item-row">
        <view class="row-item">
          <text>供应商：</text>
          <view class="company">温州新华商贸有限公司温州新华商贸有限公司</view>
        </view>
        <view class="row-item">
          <text>入库产品：</text>
          <text class="color1">6</text>项
        </view>
      </view>
      <view class="flex-sbw entry-item-row">
        <view class="row-item">
          <text>供货时间：</text> <text>2025.05.18</text>
        </view>
        <view class="row-item color2"> 待入库 </view>
        <view class="btn-view flex-center status2"> 订单详情 </view>
      </view>
    </view>
    <view class="supplier-add" @click="handleClickAdd">新增入库</view>
  </view>
</template>

<script setup lang="ts">
const handleClickLeft = () => {
  uni.navigateBack();
};
const handleClickAdd = () => {
  /* uni.navigateTo({
    url: "/pages/warehouse/supplierManage",
  }); */
};
</script>

<style scoped lang="scss">
.color1 {
  color: $primary-color;
}
.color2 {
  color: $danger-color;
}
.color3 {
  color: $warning-color;
}
.color4 {
  color: $success-color;
}
.entry-list {
  padding: 2.4vw 1.3vw;
  .entry-list-item {
    box-shadow: 0 0.33vw 0.5vw 0.5vw #e8e2e2;
    border-radius: 0.56vw;
    padding: 1.7vw;
    .entry-item-row {
      color: #515151;
      font-size: 3.73vw;
      height: 8.4vw;
      &:last-child {
        margin: 1.3vw 0;
      }
      .row-item {
        display: flex;
      }
      .company {
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        max-width: 50vw;
      }
    }
    .btn-view {
      width: 24vw;
      height: 8.4vw;
      border-radius: 0.67vw;
      color: #fff;
      font-size: 3.73vw;
      &.status1 {
        background: var(--success-color);
      }
      &.status2 {
        background: var(--warning-color);
      }
      &.status3 {
        background: var(--primary-color);
      }
      &.status4 {
        background: var(--info-color);
      }
    }
    .item-table {
      color: #515151;
      font-size: 3.73vw;
      margin-top: 1.3vw;
      .table-header {
        height: 10vw;
      }
      .table-row {
        height: 10vw;
        background-color: #f9f9f9;
        border-bottom: 1px solid #fff;
      }
      .table-cell {
        text-align: center;
        line-height: 10vw;
        flex-shrink: 0;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      .table-body {
        .table-row {
          height: 10vw;
          .table-btn {
            margin: 0 auto;
            width: 11.33vw;
            height: 6.2vw;
            border-radius: 0.67vw;
            background: $primary-color;
            color: #fff;
            font-size: 3.73vw;
          }
        }
      }
    }
  }
  .supplier-add {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 15.33vw;
    background: var(--primary-color);
    border-radius: 1.33vw 1.33vw 0vw 0vw;
    text-align: center;
    line-height: 15.33vw;
    color: #fff;
    font-size: 4.8vw;
  }
}
</style>
