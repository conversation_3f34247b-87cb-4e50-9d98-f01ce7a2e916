<template>
  <uni-nav-bar
    :border="false"
    title="库房管理"
    color="#fff"
    left-icon="left"
    @clickLeft="handleClickLeft"
    backgroundColor="var(--primary-color)"
  ></uni-nav-bar>
  <view class="house-manage">
    <view
      class="house-manage-item"
      v-for="item in warehouseList"
      :key="item.id"
    >
      <view class="house-item-title">
        <view class="row-icon">
          <image
            src="@/static/images/warehouse/house_01.png"
            mode="aspectFit"
          />
        </view>
        <view class="row-main flex-sbw">
          <view class="row-title">库房名称：{{ item.name }}</view>
          <image
            @tap="handleClickEdit(item.id, 0)"
            src="@/static/images/warehouse/edit.png"
            mode="aspectFit"
          />
        </view>
      </view>

      <view class="house-item-content">
        <view
          class="content-item flex-center"
          v-for="cItem in item.children"
          :key="cItem.id"
        >
          <view class="item-title">
            <image
              src="@/static/images/warehouse/warehouse_icon.png"
              mode="aspectFit"
            />
          </view>
          <view class="item-text"
            ><view class="row-title">{{ cItem.name }}</view>
            <image
              @tap="handleClickEdit(cItem.id, cItem.pid)"
              src="@/static/images/warehouse/edit.png"
              mode="aspectFit"
          /></view>
        </view>
        <view class="add-item" @click="handleClickAdd('ADD', 1)">+</view>
      </view>
    </view>
    <view class="house-add" @click="handleClickAdd('ADD', 0)">添加库房</view>
  </view>
  <uni-popup ref="popup" type="center" :mask-click="false">
    <view class="popup-content">
      <view class="popup-title">添加库房</view>
      <uni-forms ref="baseForm" :modelValue="baseFormData">
        <uni-easyinput
          class="popup-input"
          v-model="baseFormData.name"
          placeholder="请输入名称"
        />
        <button type="primary" @click="submit">提交</button>
      </uni-forms>
      <view class="close-view">
        <image
          @tap="handleClickClose"
          src="@/static/images/warehouse/close.png"
          mode="aspectFit"
        />
      </view>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { UniPopup } from "uni-ui-types";
import { onLoad } from "@dcloudio/uni-app";
import {
  getWarehouseList,
  addWarehouse,
  editWarehouse,
  deleteWarehouse,
} from "@/api/warehouse";
import { Response } from "@/utils/type";
const handleClickLeft = () => {
  uni.navigateBack();
};
interface Warehouse {
  id: number;
  name: string;
  pid: number;
  children?: Warehouse[];
}
interface FormItem {
  pid?: number;
  name: string;
  id?: number;
}
const warehouseList = ref<Warehouse[]>([]);
const baseFormData = reactive<FormItem>({ name: "" });
const isEdit = ref<string | number>("ADD");
const pid = ref<number>();
const popup = ref<UniPopup>();
const handleClickAdd = (type: string, id?: number) => {
  isEdit.value = type;
  if (id) {
    pid.value = id;
  }
  popup.value?.open();
};
const handleClickEdit = (id: number, perid?: number) => {
  isEdit.value = id;
  if (perid) {
    pid.value = perid;
  }
  popup.value?.open();
};
const submit = () => {
  if (!baseFormData.name) {
    return uni.showToast({
      icon: "none",
      title: "请输入名称",
    });
  }
  addNewWarehouse();
};
const handleClickClose = () => {
  popup.value?.close();
};
// 获取库房列表
const getWarehouseData = async () => {
  const res: Response = (await getWarehouseList({})) as Response;
  if (res.code === 200) {
    warehouseList.value = res.data.list;
  }
};
// 新增编辑仓库
const addNewWarehouse = async () => {
  let Fn = addWarehouse;
  const param: FormItem = {
    name: baseFormData.name,
    pid: pid.value,
  };
  if (isEdit.value !== "ADD") {
    param.id = isEdit.value as number;
    Fn = editWarehouse;
  }
  const res: Response = (await Fn(param)) as Response;
  if (res.code === 200) {
    uni.showToast({
      title: "新增成功",
      icon: "success",
    });
  }
};
onLoad(() => {
  getWarehouseData();
});
</script>

<style scoped lang="less">
.house-manage {
  padding: 3.5vw;
  .house-add {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 15.33vw;
    background: #086bdb;
    border-radius: 1.33vw 1.33vw 0vw 0vw;
    text-align: center;
    line-height: 15.33vw;
    color: #fff;
    font-size: 4.8vw;
  }

  .house-manage-item {
    padding: 3.5vw;
    background-color: #fff;
    border-radius: 0.56vw;
    box-shadow: 0 0.33vw 0.5vw 0.5vw #e8e2e2;
    color: #333333;
    font-size: 3.73vw;
    margin-bottom: 3.5vw;

    .row-icon {
      margin-right: 3.2vw;
      width: 3.73vw;
      height: 3.73vw;
      image {
        width: 100%;
        height: 100%;
      }
    }
    .house-item-title {
      height: 8.4vw;
      display: flex;
      align-items: center;
      .row-main {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: calc(100% - 8.4vw);
        image {
          width: 3.73vw;
          height: 3.73vw;
          margin-left: 1.33vw;
        }
      }
    }
    .house-item-content {
      display: flex;
      flex-wrap: wrap;
      .content-item {
        width: 25%;
        flex-direction: column;
        margin-top: 2.8vw;
        .item-text {
          display: flex;
          align-items: center;
          font-size: 3.47vw;
          padding-top: 1.33vw;
          image {
            width: 3.73vw;
            height: 3.73vw;
            margin-left: 0.67vw;
          }
        }
        .item-title {
          image {
            width: 7vw;
            height: 7vw;
          }
        }
      }
    }
  }
}
.popup-content {
  width: 80.93vw;
  height: 50vw;
  padding: 3.5vw;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 1.33vw 1.33vw 1.33vw 1.33vw;
  position: relative;
  .popup-title {
    font-size: 3.73vw;
    text-align: center;
    padding: 1vw 0 4.2vw 0;
    color: #515151;
  }
  .popup-input {
    text-align: center;
    margin-bottom: 4.2vw;
  }
  .close-view {
    position: absolute;
    bottom: -12.67vw;
    right: 35.45vw;
    &::after {
      content: "";
      display: block;
      width: 0.53vw;
      height: 5.2vw;
      background: #ffffff;
      position: absolute;
      top: -5.2vw;
      left: 3.28vw;
      // transform: rotate(45deg);
    }
    image {
      width: 7.47vw;
      height: 7.47vw;
    }
  }
}
</style>
