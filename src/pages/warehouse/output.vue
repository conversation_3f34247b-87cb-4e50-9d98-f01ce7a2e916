<template>
  <uni-nav-bar
    :border="false"
    title="订单出库"
    color="#fff"
    left-icon="left"
    @clickLeft="handleClickLeft"
    backgroundColor="var(--primary-color)"
  ></uni-nav-bar>
  <view class="output-list">
    <view class="output-list-item">
      <view class="flex-sbw output-item-row">
        <view class="row-item">
          <text>订单编号：</text>
          <text>1234567890</text>
        </view>
        <view class="row-item">
          <text>开单人员：</text>
          <text class="color1">1234567890</text>
        </view>
      </view>
      <view class="flex-sbw output-item-row">
        <view class="row-item">
          <text>关联车辆：</text>
          <text class="color3">赣A98H2T</text>
        </view>
        <view class="row-item">
          <text>出库产品：</text>
          <text class="color1">6</text>项
        </view>
        <view class="row-item color2"> 待出库 </view>
      </view>
      <view class="flex-sbw output-item-row">
        <view class="btn-view flex-center status1"> 询价采购 </view>
        <view class="btn-view flex-center status3"> 逐个出库 </view>
        <view class="btn-view flex-center status2"> 批量出库 </view>
      </view>
      <view class="item-table">
        <view class="flex-sbw table-header">
          <view
            class="table-cell"
            v-for="item in headerList"
            :key="item.key"
            :style="{ width: item.width }"
          >
            {{ item.text }}
          </view>
        </view>
        <view class="table-body">
          <view
            class="flex-sbw table-row"
            v-for="cell in tableList"
            :key="cell.id"
          >
            <view
              class="table-cell"
              v-for="item in headerList"
              :key="item.key"
              :style="{ width: item.width }"
            >
              <template v-if="item.key === 'operation'">
                <view class="table-btn flex-center"> 出库 </view>
              </template>
              <template v-else>
                {{ cell[item.key] }}
              </template>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { TableData } from "@/utils/type";
const handleClickLeft = () => {
  uni.navigateBack();
};
const headerList = ref([
  {
    text: "配件名称",
    width: "22vw",
    key: "name",
  },
  {
    text: "库房库位",
    width: "31vw",
    key: "position",
  },
  {
    text: "出库数量",
    width: "15vw",
    key: "count",
  },
  {
    text: "操作",
    width: "15vw",
    key: "operation",
  },
]);
const tableList = ref<TableData[]>([
  {
    id: 1,
    name: "下支臂L）下支臂（L）下支臂（L）",
    position: "一楼/B/3/45",
    count: "890",
  },
  {
    id: 2,
    name: "下支臂（L）",
    position: "一楼/B/3/45",
    count: "12",
  },
]);
</script>

<style scoped lang="scss">
.color1 {
  color: $primary-color;
}
.color2 {
  color: $danger-color;
}
.color3 {
  color: $warning-color;
}
.color4 {
  color: $success-color;
}

.output-list {
  padding: 2.4vw 1.3vw;
  .output-list-item {
    box-shadow: 0 0.33vw 0.5vw 0.5vw #e8e2e2;
    border-radius: 0.56vw;
    padding: 1.7vw;
    .output-item-row {
      color: #515151;
      font-size: 3.73vw;
      height: 8.4vw;
      &:last-child {
        margin: 1.3vw 0;
      }
    }
    .btn-view {
      width: 24vw;
      height: 8.4vw;
      border-radius: 0.67vw;
      color: #fff;
      font-size: 3.73vw;
      &.status1 {
        background: $success-color;
      }
      &.status2 {
        background: $warning-color;
      }
      &.status3 {
        background: $primary-color;
      }
      &.status4 {
        background: $info-color;
      }
    }
    .item-table {
      color: #515151;
      font-size: 3.73vw;
      margin-top: 1.3vw;
      .table-header {
        height: 10vw;
      }
      .table-row {
        height: 10vw;
        background-color: #f9f9f9;
        border-bottom: 1px solid #fff;
      }
      .table-cell {
        text-align: center;
        line-height: 10vw;
        flex-shrink: 0;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
      .table-body {
        .table-row {
          height: 10vw;
          .table-btn {
            margin: 0 auto;
            width: 11.33vw;
            height: 6.2vw;
            border-radius: 0.67vw;
            background: $primary-color;
            color: #fff;
            font-size: 3.73vw;
          }
        }
      }
    }
  }
}
</style>
