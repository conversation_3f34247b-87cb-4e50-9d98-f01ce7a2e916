<template>
  <uni-nav-bar
    :border="false"
    title="供应商管理"
    color="#fff"
    left-icon="left"
    @clickLeft="handleClickLeft"
    backgroundColor="var(--primary-color)"
  ></uni-nav-bar>
  <view class="supplier-manage">
    <view
      class="supplier-manage-item"
      v-for="item in supplierList"
      :key="item.id"
    >
      <view class="supplier-item-title">
        <view class="row-icon">
          <uni-icons type="contact" size="5.2vw"></uni-icons>
        </view>
        <view class="row-main flex-sbw">
          <view class="row-title">{{ item.supplier_name }}</view>
          <image
            @tap="handleClickAdd(item.id as number)"
            src="@/static/images/warehouse/edit.png"
            mode="aspectFit"
          />
        </view>
      </view>
      <view class="supplier-item-row">
        <view class="row-icon">
          <uni-icons type="contact" size="5.2vw"></uni-icons>
        </view>
        <view class="row-text">{{ item.address }}</view>
      </view>
      <view class="supplier-item-row">
        <view class="row-icon">
          <uni-icons type="contact" size="5.2vw"></uni-icons>
        </view>
        <view class="row-text">{{ item.category }}</view>
      </view>
      <view class="supplier-item-row">
        <view class="row-icon">
          <uni-icons type="contact" size="5.2vw"></uni-icons>
        </view>
        <view class="row-content">
          <view class="content-item">
            <text class="item-title">联系人：</text>
            <text class="item-text">{{ item.contact }}</text>
          </view>
          <view class="content-item">
            <text class="item-title">联系电话：</text>
            <text class="item-text">{{ item.phone }}</text>
            <image src="@/static/images/warehouse/phone.png" mode="aspectFit" />
          </view>
        </view>
      </view>
      <view class="supplier-item-row">
        <view class="row-icon">
          <uni-icons type="contact" size="5.2vw"></uni-icons>
        </view>
        <view class="row-text">{{ item.notes }}</view>
      </view>
    </view>
    <view class="supplier-add" @click="handleClickAdd('ADD')">添加供应商</view>
  </view>
  <uni-popup ref="popup" type="center" :mask-click="false">
    <view class="popup-content">
      <view class="popup-title">添加供应商</view>
      <uni-forms ref="baseForm" :modelValue="baseFormData">
        <uni-easyinput
          class="popup-input"
          v-model="baseFormData.supplier_name"
          placeholder="供应商名称"
        />
        <uni-easyinput
          class="popup-input"
          v-model="baseFormData.contact"
          placeholder="联系人"
        />
        <uni-easyinput
          class="popup-input"
          v-model="baseFormData.phone"
          placeholder="联系电话"
        />
        <uni-easyinput
          class="popup-input"
          v-model="baseFormData.category"
          placeholder="主营产品"
        />
        <uni-easyinput
          class="popup-input"
          v-model="baseFormData.address"
          placeholder="联系地址"
        />
        <uni-easyinput
          style="margin-bottom: 4.2vw"
          v-model="baseFormData.notes"
          type="textarea"
          placeholder="备注"
        />
        <button type="primary" @click="submit">提交</button>
      </uni-forms>
      <view class="close-view">
        <image
          @tap="handleClickClose"
          src="@/static/images/warehouse/close.png"
          mode="aspectFit"
        />
      </view>
    </view>
  </uni-popup>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import type { UniPopup } from "uni-ui-types";
import type { Response, TableData } from "@/utils/type";
import {
  addSupplier,
  editSupplier,
  getSupplierList,
  getSupplierDetail,
} from "@/api/warehouse";
interface SupplierList {
  id: number;
  company_id: number;
  store_id: number;
  supplier_name: string;
  province_id: number;
  city_id: number;
  districtcounty_id: number;
  address: string;
  phone: string;
  contact: string;
  category_ids: string;
  category: string;
  is_delete: number;
  category_info: string;
  notes: string;
}
interface FormObject {
  supplier_name: string; //供应商名称
  contact: string; //联系人
  phone: string; //联系电话
  category: string; //经营范围
  province_id: string; //省份ID
  city_id: string; //城市ID
  districtcounty_id: string; //区县ID
  address: string; //详细地址
  notes: string; //备注
  type: number; //供应商类型1内部供应商2外部供应商
  [key: string]: any;
}
const supplierList = ref<SupplierList[]>([]);
const isEdit = ref<string | number>("ADD");
const handleClickLeft = () => {
  uni.navigateBack();
};
const baseFormData = reactive<FormObject>({
  supplier_name: "",
  contact: "",
  phone: "",
  category: "",
  province_id: "",
  city_id: "",
  districtcounty_id: "",
  address: "",
  notes: "",
  type: 1,
});
const popup = ref<UniPopup>();
const handleClickAdd = (val: string | number) => {
  isEdit.value = val;
  if (val !== "ADD") {
    getSupplierDetailData(val as number);
  }
  popup.value?.open();
};
const submit = () => {
  // 表单验证
  if (
    !baseFormData.supplier_name ||
    !baseFormData.contact ||
    !baseFormData.phone
  ) {
    uni.showToast({
      title: "请输入必填项",
      icon: "none",
    });
    return;
  }
  addSupplierData();
};
const handleClickClose = () => {
  popup.value?.close();
};
// 获取供应商列表
const getSupplierListData = async () => {
  const res = (await getSupplierList({})) as Response;
  if (res.code === 200) {
    supplierList.value = res.data.list;
  }
};
// 新增供应商
const addSupplierData = async () => {
  const params: TableData = {
    name: baseFormData.supplier_name,
    contact: baseFormData.contact,
    phone: baseFormData.phone,
  };
  let request = addSupplier;
  if (isEdit.value !== "ADD") {
    params.id = isEdit.value;
    request = editSupplier;
  }
  const res = (await request(params)) as Response;
  if (res.code === 200) {
    getSupplierListData();
  }
};
// 获取供应商详情
const getSupplierDetailData = async (id: number) => {
  const res = (await getSupplierDetail({ id })) as Response;
  if (res.code === 200) {
    for (const key in baseFormData) {
      baseFormData[key] = res.data[key];
    }
  }
};
getSupplierListData();
</script>

<style scoped lang="scss">
.supplier-manage {
  padding: 3.5vw;
  .supplier-add {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 15.33vw;
    background: var(--primary-color);
    border-radius: 1.33vw 1.33vw 0vw 0vw;
    text-align: center;
    line-height: 15.33vw;
    color: #fff;
    font-size: 4.8vw;
  }

  .supplier-manage-item {
    padding: 3.5vw;
    background-color: #fff;
    border-radius: 0.56vw;
    box-shadow: 0 0.33vw 0.5vw 0.5vw #e8e2e2;
    color: #333333;
    font-size: 3.73vw;
    margin-bottom: 3.5vw;

    .row-icon {
      margin-right: 3.2vw;
    }
    .supplier-item-title {
      height: 8.4vw;
      display: flex;
      align-items: center;
      .row-main {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: calc(100% - 8.4vw);
        image {
          width: 3.73vw;
          height: 3.73vw;
          margin-left: 1.33vw;
        }
      }
    }
    .supplier-item-row {
      display: flex;
      height: 7.8vw;
      align-items: center;
      .row-content {
        width: calc(100% - 8.4vw);
        display: flex;
        align-items: center;
        justify-content: space-between;
        .content-item {
          display: flex;
          align-items: center;
          image {
            width: 3.73vw;
            height: 3.73vw;
            margin-left: 1.33vw;
          }
        }
      }
    }
  }
}
.popup-content {
  width: 80.93vw;
  height: 130.53vw;
  padding: 3.5vw;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 1.33vw 1.33vw 1.33vw 1.33vw;
  position: relative;
  .popup-title {
    font-size: 3.73vw;
    text-align: center;
    padding: 1vw 0 4.2vw 0;
    color: #515151;
  }
  .popup-input {
    text-align: center;
    margin-bottom: 4.2vw;
  }
  .close-view {
    position: absolute;
    bottom: -12.67vw;
    right: 35.45vw;
    &::after {
      content: "";
      display: block;
      width: 0.53vw;
      height: 5.2vw;
      background: #ffffff;
      position: absolute;
      top: -5.2vw;
      left: 3.28vw;
      // transform: rotate(45deg);
    }
    image {
      width: 7.47vw;
      height: 7.47vw;
    }
  }
}
</style>
