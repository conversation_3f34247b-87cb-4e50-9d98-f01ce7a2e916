<template>
  <uni-nav-bar
    title="入库订单"
    left-text="返回"
    @clickLeft="handleClickLeft"
    backgroundColor="var(--primary-color)"
  ></uni-nav-bar>
  <view class="entry-order">
    <view class="entry-order-item">
      <view class="entry-order-item-row">
        <view class="entry-order-item-row-item">
          <text>订单编号：</text>
          <text>1234567890</text>
        </view>
        <view class="entry-order-item-row-item">
          <text>入库人员：</text>
          <text>刘小明</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
const handleClickLeft = () => {
  uni.navigateBack();
};
</script>

<style scoped lang="scss"></style>
