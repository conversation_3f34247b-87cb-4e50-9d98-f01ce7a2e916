<template>
  <uni-nav-bar
    :border="false"
    title="库房管理"
    color="#fff"
    left-icon="left"
    @clickLeft="handleClickLeft"
    backgroundColor="var(--primary-color)"
  ></uni-nav-bar>
  <view class="warehouse-manage">
    <view class="ware-count-list">
      <view class="ware-count-item">
        <view class="item-num">12</view>
        <view class="item-text">本月出库</view> </view
      ><view class="ware-count-item">
        <view class="item-num">12</view>
        <view class="item-text">当前库存</view> </view
      ><view class="ware-count-item">
        <view class="item-num">12</view>
        <view class="item-text">本月毛利</view>
      </view>
    </view>
    <view class="menu-list">
      <view
        class="menu-list-item flex-center"
        v-for="item in menuList"
        :key="item.text"
      >
        <view
          @tap="handleClickMenu(item.path)"
          class="menu-list-item-icon flex-center"
          :style="{ background: item.color }"
        >
          <image :src="item.icon" mode="aspectFit" />
        </view>
        <view class="menu-list-item-text">{{ item.text }}</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { importFile } from "@/utils/file";
const handleClickLeft = () => {
  uni.navigateBack();
};
const menuList = ref([
  {
    icon: importFile("../static/images/warehouse/warehouse01.png"),
    text: "订单出库",
    color: "#3456AC",
    path: "/pages/warehouse/output",
  },
  {
    icon: importFile("../static/images/warehouse/warehouse02.png"),
    text: "采购入库",
    color: "#248145",
    path: "/pages/warehouse/entry",
  },
  {
    icon: importFile("../static/images/warehouse/warehouse03.png"),
    text: "供应商管理",
    color: "#565049",
    path: "/pages/warehouse/supplierManage",
  },
  {
    icon: importFile("../static/images/warehouse/warehouse04.png"),
    text: "库房管理",
    color: "#B4B2B0",
    path: "/pages/warehouse/warehouseManage",
  },
  {
    icon: importFile("../static/images/warehouse/warehouse05.png"),
    text: "库存管理",
    color: "#1590F8",
    path: "/pages/warehouse/inventoryManage",
  },
  {
    icon: importFile("../static/images/warehouse/warehouse06.png"),
    text: "库房盘点",
    color: "#E8B174",
    path: "/pages/warehouse/warehouseStocktaking",
  },
]);
const handleClickMenu = (path: string) => {
  uni.navigateTo({
    url: path,
    fail: (err) => {
      uni.showToast({
        title: "模块开发中",
        icon: "error",
      });
    },
  });
};
</script>

<style scoped lang="less">
.warehouse-manage {
  padding: 3.5vw;
  .ware-count-list {
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: 27.6vw;
    box-shadow: 0 0.33vw 0.5vw 0.5vw #e8e2e2;
    border-radius: 0.56vw;
    .ware-count-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .item-num {
        font-size: 4.8vw;
        font-weight: bold;
        color: #ee840e;
        margin-bottom: 5.8vw;
      }
      .item-text {
        font-size: 3.73vw;
        color: #515151;
      }
    }
  }
  .menu-list {
    width: 100%;
    background: #fff;
    margin-top: 2.47vw;
    display: flex;
    flex-wrap: wrap;
    box-sizing: border-box;
    .menu-list-item {
      width: 33%;
      height: 46vw;
      flex-direction: column;
      .menu-list-item-icon {
        width: 19vw;
        height: 19vw;
        border-radius: 50%;
        background: #f4691e;
        image {
          width: 9.4vw;
          height: 9.4vw;
        }
      }

      .menu-list-item-text {
        font-size: 3.73vw;
        color: #333333;
        margin-top: 4vw;
      }
    }
  }
}
</style>
