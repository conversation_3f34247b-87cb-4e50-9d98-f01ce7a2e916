<template>
  <uni-nav-bar
    title="入库详情"
    left-text="返回"
    @clickLeft="handleClickLeft"
    backgroundColor="var(--primary-color)"
  ></uni-nav-bar>
  <view class="entry-detail">
    <view class="entry-detail-item">
      <view class="entry-detail-item-row">
        <view class="entry-detail-item-row-item">
          <text>订单编号：</text>
          <text>1234567890</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
const handleClickLeft = () => {
  uni.navigateBack();
};
</script>

<style scoped lang="scss"></style>
