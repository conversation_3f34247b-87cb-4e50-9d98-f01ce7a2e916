<template>
  <view class="exchange-detail-page">
    <!-- 顶部图片轮播 -->
    <view class="swiper-wrap">
      <swiper class="goods-swiper" indicator-dots circular>
        <swiper-item v-for="(img, idx) in imgs" :key="idx">
          <image :src="img" class="swiper-img" mode="aspectFill" />
        </swiper-item>
      </swiper>
      <view class="swiper-index">{{ current + 1 }}/{{ imgs.length }}</view>
    </view>
    <!-- 工分条+头像组 -->
    <view class="score-bar">
      <text class="score-num">9000工分</text>
      <view class="avatar-group">
        <image v-for="(avatar, idx) in avatars" :key="idx" :src="avatar" class="avatar" />
      </view>
    </view>
    <!-- 商品标题 -->
    <view class="goods-title">
      70 迈 A800 4K 智能行车记录仪-高清超大储存-远光自然规避-红外夜视记录仪
    </view>
    <!-- 商品详情卡片 -->
    <view class="goods-detail-card">
      <view class="detail-title">
        商品详情
        <uni-icons type="help" size="18" color="#bbb" />
      </view>
      <view class="detail-row">
        <text class="detail-label">商品产地：</text>
        <text class="detail-value">广东省</text>
        <text class="detail-label">发货地：</text>
        <text class="detail-value">四川省成都市</text>
      </view>
      <view class="detail-row">
        <text class="detail-label">商品尺寸：</text>
        <text class="detail-value">长130cm * 宽160cm * 高80</text>
      </view>
      <view class="detail-row">
        <text class="detail-label">商品材质：</text>
        <text class="detail-value">原厂配套</text>
      </view>
    </view>
    <!-- 商品介绍 -->
    <view class="goods-desc-card">
      这是一段产品介绍，商品采用天然材质按按时打卡是快乐到家啊啊考虑实际的阿斯达克克亥克速度发卡发卡上单划分开来卡萨丁很快发货送货
    </view>
    <!-- 售后服务保障 -->
    <view class="service-card">
      <view class="service-title">
        售后服务保障
        <text class="not-included">本商品不包邮</text>
        <uni-icons type="sound" size="18" color="#ff4d4f" />
      </view>
      <view class="service-list">
        <view class="service-item">1、本产品由太平洋保险承保，任何质量问题都可以解决</view>
        <view class="service-item">2、产品使用过程中，请谨遵8岁以上儿童</view>
        <view class="service-item">3、收到产品后，请检查产品是否完整</view>
        <view class="service-item">4、正品质保请联系官方客服</view>
      </view>
    </view>
    <!-- 底部栏 -->
    <view class="bottom-bar">
      <view class="limit-info">限兑 1 剩余 13</view>
      <button class="exchange-btn">立即兑换</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
const imgs = ref([
  'https://img1.jpg',
  'https://img2.jpg',
  'https://img3.jpg',
  'https://img4.jpg',
  'https://img5.jpg',
]);
const avatars = ref([
  'https://avatar1.jpg',
  'https://avatar2.jpg',
  'https://avatar3.jpg',
  'https://avatar4.jpg',
  'https://avatar5.jpg',
  'https://avatar6.jpg',
  'https://avatar7.jpg',
]);
const current = ref(0);
</script>

<style lang="scss" scoped>
.exchange-detail-page {
  min-height: 100vh;
  background: #f8f8f8;
  .swiper-wrap {
    position: relative;
    .goods-swiper {
      width: 100vw;
      height: 320rpx;
      .swiper-img {
        width: 100vw;
        height: 320rpx;
        object-fit: cover;
      }
    }
    .swiper-index {
      position: absolute;
      right: 24rpx;
      bottom: 16rpx;
      background: rgba(0,0,0,0.4);
      color: #fff;
      font-size: 24rpx;
      border-radius: 24rpx;
      padding: 4rpx 18rpx;
    }
  }
  .score-bar {
    background: #1686f9;
    color: #fff;
    display: flex;
    align-items: center;
    padding: 0 24rpx;
    height: 64rpx;
    .score-num {
      font-size: 32rpx;
      font-weight: bold;
    }
    .avatar-group {
      display: flex;
      align-items: center;
      margin-left: 32rpx;
      .avatar {
        width: 44rpx;
        height: 44rpx;
        border-radius: 50%;
        border: 2rpx solid #fff;
        margin-left: -12rpx;
        background: #eee;
      }
      .avatar:first-child {
        margin-left: 0;
      }
    }
  }
  .goods-title {
    font-size: 32rpx;
    color: #222;
    font-weight: 600;
    margin: 32rpx 24rpx 0 24rpx;
    line-height: 1.4;
  }
  .goods-detail-card {
    background: #fff;
    border-radius: 16rpx;
    margin: 24rpx 24rpx 0 24rpx;
    padding: 24rpx;
    .detail-title {
      font-size: 28rpx;
      color: #222;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8rpx;
      margin-bottom: 18rpx;
    }
    .detail-row {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      color: #444;
      margin-bottom: 10rpx;
      .detail-label {
        color: #888;
        margin-right: 8rpx;
      }
      .detail-value {
        color: #222;
        margin-right: 24rpx;
      }
    }
  }
  .goods-desc-card {
    background: #fff;
    border-radius: 16rpx;
    margin: 24rpx 24rpx 0 24rpx;
    padding: 24rpx;
    font-size: 26rpx;
    color: #666;
    line-height: 1.7;
  }
  .service-card {
    background: #fff;
    border-radius: 16rpx;
    margin: 24rpx 24rpx 0 24rpx;
    padding: 24rpx;
    .service-title {
      font-size: 28rpx;
      color: #222;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 12rpx;
      margin-bottom: 18rpx;
      .not-included {
        color: #ff4d4f;
        font-size: 26rpx;
        margin-left: 18rpx;
      }
    }
    .service-list {
      .service-item {
        font-size: 26rpx;
        color: #444;
        margin-bottom: 8rpx;
        display: flex;
        align-items: center;
        .icon {
          margin-left: 8rpx;
        }
      }
    }
  }
  .bottom-bar {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100vw;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24rpx 24rpx 24rpx;
    box-shadow: 0 -2rpx 12rpx 0 #eee;
    .limit-info {
      color: #bbb;
      font-size: 26rpx;
    }
    .exchange-btn {
      background: #ff9000;
      color: #fff;
      border-radius: 8rpx;
      font-size: 32rpx;
      height: 88rpx;
      padding: 0 64rpx;
      margin-left: 32rpx;
    }
  }
}
</style> 