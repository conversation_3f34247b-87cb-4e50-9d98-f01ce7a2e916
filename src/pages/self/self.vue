<template>
  <view class="self-page">
    <!-- 顶部蓝色背景及用户信息 -->
    <view class="top-bg">
      <view class="top-bar">
        <view class="status-bar"></view>
      </view>
      <view class="user-info">
        <image
          class="avatar"
          :src="userInfo.avatar || '/static/default-avatar.png'"
          mode="aspectFill"
        />
        <view class="user-main">
          <view class="user-name">
            {{ userInfo.fullname || "暂无" }}
            <text class="user-name-text">{{
              userInfo.store_name || "暂无"
            }}</text>
          </view>
          <view class="user-icons">
            <view class="icon-border">
              <uv-icon size="14" name="phone" color="#fff"></uv-icon>
            </view>
            <view class="icon-border">
              <uv-icon size="14" name="chat" color="#fff"></uv-icon>
            </view>
            <view class="icon-border">
              <uv-icon size="14" name="qq-fill" color="#fff"></uv-icon>
            </view>
          </view>
        </view>
        <view class="setting-btn" @tap="onSetting">
          <uni-icons type="gear" size="18" color="#fff" />
          <view class="setting-text">设置</view>
        </view>
      </view>
    </view>

    <!-- 余额/优惠券/工分币 卡片 -->
    <view class="card-row">
      <view class="card-item">
        <view class="card-label">我的余额</view>
        <view class="card-value money">1540.00</view>
      </view>
      <view class="card-item">
        <view class="card-label">优惠券</view>
        <view class="card-value">3</view>
      </view>
      <view class="card-item">
        <view class="card-label">工分币</view>
        <view class="card-value">89</view>
      </view>
    </view>

    <!-- 工分任务卡片 -->
    <view class="task-card">
      <view class="task-header">
        <view class="task-title">工分任务</view>
        <view class="task-link">挣工分兑好礼</view>
      </view>
      <view class="task-list">
        <view class="task-item">
          <view class="task-icon blue">
            <uv-icon size="16" name="calendar" color="#fff"></uv-icon>
          </view>
          <view class="task-info">
            <view class="task-name"
              >签到 <text class="task-score">+1</text></view
            >
            <view class="task-desc">连续签到有额外工分赠送</view>
          </view>
          <button class="task-btn" @tap="onCheckin">去完成</button>
        </view>
        <view class="task-item">
          <view class="task-icon blue">
            <uv-icon size="16" name="star" color="#fff"></uv-icon>
          </view>
          <view class="task-info">
            <view class="task-name"
              >评价我们 <text class="task-score orange">+2</text></view
            >
            <view class="task-desc">给个好评让更多汽修人得到实惠</view>
          </view>
          <button class="task-btn">去完成</button>
        </view>
        <view class="task-item">
          <view class="task-icon blue">
            <uv-icon size="16" name="share-square" color="#fff"></uv-icon>
          </view>
          <view class="task-info">
            <view class="task-name"
              >转发分享 <text class="task-score orange">+3</text></view
            >
            <view class="task-desc">分享微信朋友圈</view>
          </view>
          <button class="task-btn">去完成</button>
        </view>
      </view>
    </view>

    <!-- 底部功能区 -->
    <view class="bottom-menu">
      <view class="menu-item">
        <view class="menu-icon blue">
          <uv-icon size="20" name="red-packet" color="#fff"></uv-icon>
        </view>
        <view class="menu-label">工分商城</view>
      </view>
      <view class="menu-item">
        <view class="menu-icon brown">
          <uv-icon size="20" name="map-fill" color="#fff"></uv-icon>
        </view>
        <view class="menu-label">收货地址</view>
      </view>
      <view class="menu-item">
        <view class="menu-icon orange">
          <uv-icon size="20" name="question-circle" color="#fff"></uv-icon>
        </view>
        <view class="menu-label">关于系统</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue"
import { getStoreUserInfo } from "@/api/login"
import type { Response, TableData } from "@/utils/type"
const userInfo = ref<TableData>({})
const getUserInfo = async () => {
  const res: Response = (await getStoreUserInfo({})) as Response
  if (res.code === 200) {
    userInfo.value = res.data
  }
}
const onSetting = () => {
  uni.navigateTo({ url: "/pages/self/setting" })
}
const onCheckin = () => {
  uni.navigateTo({ url: "/pages/self/checkin" })
}
const onBack = () => {
  uni.navigateBack()
}
onMounted(() => {
  getUserInfo()
})
</script>

<style lang="scss" scoped>
.self-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 32rpx;

  .top-bg {
    background: #1686f9;
    border-bottom-left-radius: 24rpx;
    border-bottom-right-radius: 24rpx;
    padding-bottom: 32rpx;
    position: relative;
    .top-bar {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      height: 50rpx;
      .status-bar {
        height: 40rpx;
      }
    }
    .user-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8rpx;
      padding: 32rpx;
      .avatar {
        width: 110rpx;
        height: 110rpx;
        border-radius: 50%;
        border: 4rpx solid #fff;
        margin-right: 16rpx;
      }
      .user-main {
        flex: 1;
      }
      .user-name {
        color: #fff;
        font-size: 30rpx;
        margin-bottom: 8rpx;
        max-width: 420rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .user-icons {
        display: flex;
        gap: 24rpx;
        margin-top: 18rpx;
        .icon-border {
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid #fff;
        }
      }
      .setting-btn {
        text-align: center;
        .setting-text {
          color: #fff;
          font-size: 26rpx;
          margin-top: 14rpx;
        }
      }
    }
  }

  .card-row {
    display: flex;
    background: #fff;
    border-radius: 16rpx;
    margin: -40rpx 24rpx 24rpx 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(22, 134, 249, 0.08);
    overflow: hidden;
    z-index: 1;
    position: relative;
    .card-item {
      flex: 1;
      text-align: center;
      padding: 28rpx 0 18rpx 0;
      .card-label {
        color: #888;
        font-size: 26rpx;
        margin-bottom: 8rpx;
      }
      .card-value {
        font-size: 32rpx;
        color: #333;
        font-weight: bold;
        &.money {
          color: #ff6600;
        }
      }
    }
  }

  .task-card {
    background: #fff;
    border-radius: 16rpx;
    margin: 0 24rpx 24rpx 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(22, 134, 249, 0.08);
    .task-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx 24rpx 0 24rpx;
      .task-title {
        font-size: 30rpx;
        color: #222;
        font-weight: bold;
      }
      .task-link {
        font-size: 26rpx;
        color: #1686f9;
      }
    }
    .task-list {
      padding: 0 24rpx 24rpx 24rpx;
      .task-item {
        display: flex;
        align-items: center;
        padding: 24rpx 0;
        border-bottom: 1px solid #f5f5f5;
        &:last-child {
          border-bottom: none;
        }
        .task-icon {
          width: 60rpx;
          height: 60rpx;
          border-radius: 50%;
          background: #1686f9;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20rpx;
          &.blue {
            background: #1686f9;
          }
        }
        .task-info {
          flex: 1;
          .task-name {
            font-size: 28rpx;
            color: #222;
            .task-score {
              color: #1686f9;
              margin-left: 4rpx;
            }
            .task-score.orange {
              color: #ff6600;
            }
          }
          .task-desc {
            font-size: 24rpx;
            color: #888;
            margin-top: 4rpx;
          }
        }
        .task-btn {
          background: #1686f9;
          color: #fff;
          font-size: 26rpx;
          border-radius: 24rpx;
          padding: 0 32rpx;
          height: 56rpx;
          line-height: 56rpx;
        }
      }
    }
  }

  .bottom-menu {
    display: flex;
    background: #fff;
    border-radius: 16rpx;
    margin: 0 24rpx;
    box-shadow: 0 4rpx 16rpx rgba(22, 134, 249, 0.08);
    padding: 24rpx 0;
    justify-content: space-around;
    .menu-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
      .menu-icon {
        width: 64rpx;
        height: 64rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8rpx;
        &.blue {
          background: #1686f9;
        }
        &.brown {
          background: #a97c50;
        }
        &.orange {
          background: #ff9900;
        }
      }
      .menu-label {
        font-size: 26rpx;
        color: #333;
        margin-top: 4rpx;
      }
    }
  }
}
</style>
