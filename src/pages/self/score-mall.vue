<template>
  <view class="score-mall-page">
    <!-- 顶部栏 -->
    <uni-nav-bar
      fixed
      status-bar
      title="工分商城"
      left-icon="left"
      background-color="#1686f9"
      color="#fff"
      @click-left="onBack"
    >
      <template #right>
        <text class="service-btn">客服</text>
      </template>
    </uni-nav-bar>
    <!-- 当前工分卡片 -->
    <view class="score-card">
      <view class="score-bg">
        <view class="score-info">
          <text class="score-label">当前工分</text>
          <text class="score-value">{{ score }}</text>
        </view>
        <view class="score-action" @click="onGoEarn">去赚工分 <uni-icons type="right" size="16" color="#ffb300" /></view>
      </view>
    </view>
    <!-- tab切换 -->
    <view class="tab-bar">
      <view :class="['tab-item', tabIndex === 0 ? 'active' : '']" @click="tabIndex = 0">积分排序</view>
      <view :class="['tab-item', tabIndex === 1 ? 'active' : '']" @click="tabIndex = 1">我的记录</view>
    </view>
    <!-- 商品列表 -->
    <view v-if="tabIndex === 0" class="goods-list">
      <view v-for="item in goodsList" :key="item.id" class="goods-card">
        <image :src="item.img" class="goods-img" mode="aspectFill" />
        <view class="goods-title">{{ item.title }}</view>
        <view class="goods-desc">{{ item.desc }}</view>
        <view class="goods-score">
          <text class="score-main">{{ item.score }}工分</text>
          <text v-if="item.extra" class="score-extra">+{{ item.extra }}</text>
          <button class="exchange-btn">兑换</button>
        </view>
        <view class="goods-limit">限兑{{ item.limit }} 剩余{{ item.left }}</view>
      </view>
    </view>
    <!-- 我的记录tab可后续补充 -->
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
const score = ref(7020);
const tabIndex = ref(0);
const goodsList = ref([
  {
    id: 1,
    img: 'https://img1.jpg',
    title: 'PINPINK蓝牙音箱-尊贵音质不同凡响-双频…',
    desc: '',
    score: 9000,
    extra: '',
    limit: 1,
    left: 13,
  },
  {
    id: 2,
    img: 'https://img2.jpg',
    title: 'PINPINK蓝牙音箱-尊贵音质不同凡响-双频…',
    desc: '',
    score: 6800,
    extra: '30元',
    limit: 1,
    left: 13,
  },
  {
    id: 3,
    img: 'https://img3.jpg',
    title: '工具推科汽修抽屉五金工具箱…',
    desc: '',
    score: 6800,
    extra: '3元',
    limit: 1,
    left: 13,
  },
  {
    id: 4,
    img: 'https://img4.jpg',
    title: '奇瑞两层三层铁皮工具箱加厚多多功能…',
    desc: '',
    score: 230,
    extra: '100元',
    limit: 1,
    left: 13,
  },
]);
const onBack = () => {
  uni.navigateBack();
};
const onGoEarn = () => {
  uni.showToast({ title: '去赚工分', icon: 'none' });
};
</script>

<style lang="scss" scoped>
.score-mall-page {
  min-height: 100vh;
  background: #f8f8f8;
  .service-btn {
    color: #fff;
    font-size: 28rpx;
    margin-right: 24rpx;
  }
  .score-card {
    margin: 32rpx 24rpx 0 24rpx;
    .score-bg {
      background: linear-gradient(120deg, #1686f9 60%, #ffb300 100%);
      border-radius: 20rpx;
      padding: 40rpx 32rpx 32rpx 32rpx;
      position: relative;
      overflow: hidden;
      .score-info {
        .score-label {
          color: #fff;
          font-size: 28rpx;
        }
        .score-value {
          color: #fff;
          font-size: 56rpx;
          font-weight: bold;
          margin-top: 8rpx;
        }
      }
      .score-action {
        position: absolute;
        right: 32rpx;
        top: 40rpx;
        color: #ffb300;
        font-size: 28rpx;
        display: flex;
        align-items: center;
      }
    }
  }
  .tab-bar {
    display: flex;
    background: #fff;
    border-radius: 16rpx;
    margin: 32rpx 24rpx 0 24rpx;
    overflow: hidden;
    .tab-item {
      flex: 1;
      text-align: center;
      padding: 28rpx 0;
      font-size: 30rpx;
      color: #666;
      &.active {
        color: #1686f9;
        font-weight: bold;
        border-bottom: 4rpx solid #1686f9;
        background: #f8f8f8;
      }
    }
  }
  .goods-list {
    display: flex;
    flex-wrap: wrap;
    gap: 24rpx;
    margin: 32rpx 24rpx 0 24rpx;
    .goods-card {
      width: calc(50% - 12rpx);
      background: #fff;
      border-radius: 16rpx;
      box-shadow: 0 4rpx 16rpx 0 #e6e6e6;
      margin-bottom: 24rpx;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      .goods-img {
        width: 100%;
        height: 200rpx;
        object-fit: cover;
      }
      .goods-title {
        font-size: 28rpx;
        color: #222;
        margin: 16rpx 16rpx 0 16rpx;
        font-weight: 500;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .goods-desc {
        font-size: 24rpx;
        color: #888;
        margin: 8rpx 16rpx 0 16rpx;
        height: 32rpx;
        overflow: hidden;
      }
      .goods-score {
        display: flex;
        align-items: center;
        margin: 16rpx 16rpx 0 16rpx;
        .score-main {
          color: #ff9000;
          font-size: 28rpx;
          font-weight: bold;
        }
        .score-extra {
          color: #ff9000;
          font-size: 24rpx;
          margin-left: 8rpx;
        }
        .exchange-btn {
          margin-left: auto;
          background: #ff9000;
          color: #fff;
          border-radius: 8rpx;
          font-size: 26rpx;
          padding: 8rpx 32rpx;
        }
      }
      .goods-limit {
        font-size: 22rpx;
        color: #bbb;
        margin: 12rpx 16rpx 16rpx 16rpx;
      }
    }
  }
}
</style> 