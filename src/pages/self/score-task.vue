<template>
  <view class="score-task-page">
    <!-- 顶部栏 -->
    <uni-nav-bar
      fixed
      status-bar
      title="工分任务"
      left-icon="left"
      background-color="#1686f9"
      color="#fff"
      @click-left="onBack"
    />
    <!-- 顶部蓝色卡片 -->
    <view class="top-card">
      <view class="left-info">
        <view class="days">3<text class="unit">天</text></view>
        <view class="desc">您已连续签到</view>
        <view class="reward"
          >连续签到<text class="highlight">14天</text>可额外获得<text
            class="highlight"
            >10个工分</text
          ></view
        >
      </view>
      <button
        class="sign-btn"
        :class="{ disabled: hasCheckedIn }"
        @click="handleCheckin"
        :disabled="hasCheckedIn"
      >
        {{ hasCheckedIn ? "已签到" : "签到" }}
      </button>
      <image class="task-ill" src="https://img-task-ill.png" mode="widthFix" />
    </view>
    <!-- 月份切换 -->
    <view class="month-bar">
      <view
        class="month"
        v-for="(m, idx) in months"
        :key="m"
        :class="{ active: idx === monthIndex }"
        @click="monthIndex = idx"
        >{{ m }}</view
      >
    </view>
    <!-- 日历签到 -->
    <view class="calendar-card">
      <view class="calendar-head">
        <view v-for="d in weekDays" :key="d" class="calendar-week">{{
          d
        }}</view>
      </view>
      <view class="calendar-body">
        <view
          v-for="(day, idx) in calendar"
          :key="idx"
          class="calendar-day"
          :class="day.status"
        >
          <text>{{ day.text }}</text>
          <view v-if="day.status === 'signed'" class="signed-text">已签</view>
          <view v-else-if="day.status === 'miss'" class="miss-text">未签</view>
        </view>
      </view>
    </view>
    <!-- 签到规则 -->
    <view class="rule-card">
      <view class="rule-title">签到规则</view>
      <view class="rule-list">
        <view class="rule-item"
          >1、每日普通签到将获得1个工分币，连续签到（两周内）达到14天后，额外获得8个工分币。</view
        >
        <view class="rule-item">2、签到任务每天0点,12点以后自动更新。</view>
        <view class="rule-item">3、除签到以外的上币任务随机发放。</view>
        <view class="rule-item"
          >4、在自然月（月清零，周后再周）计算过程中出现一次未签到，将失去额外工分奖励，一个月共计两次额外赠送工分币机会。</view
        >
        <view class="rule-item"
          >5、工分币可以用于商品兑换，兑换规则详见工分商城页面品兑换说明，本站规则最终解释权归平台自助平台所有。</view
        >
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue"
const months = ref(["2024/07", "2024/08", "2024/09"])
const monthIndex = ref(1)
const weekDays = ["日", "一", "二", "三", "四", "五", "六"]
const hasCheckedIn = ref(false) // 今日是否已签到
// 日历数据示例
const calendar = ref([
  { text: "", status: "" },
  { text: "", status: "" },
  { text: "", status: "" },
  { text: "", status: "" },
  { text: "", status: "" },
  { text: "", status: "" },
  { text: "1", status: "miss" },
  { text: "2", status: "signed" },
  { text: "3", status: "signed" },
  { text: "4", status: "miss" },
  { text: "5", status: "signed" },
  { text: "6", status: "signed" },
  { text: "7", status: "signed" },
  { text: "8", status: "miss" },
  { text: "9", status: "signed" },
  { text: "10", status: "" },
  { text: "11", status: "signed" },
  { text: "12", status: "signed" },
  { text: "13", status: "" },
  { text: "14", status: "" },
  { text: "15", status: "" },
  { text: "16", status: "" },
  { text: "17", status: "" },
  { text: "18", status: "" },
  { text: "19", status: "" },
  { text: "20", status: "" },
  { text: "21", status: "" },
  { text: "22", status: "" },
  { text: "23", status: "" },
  { text: "24", status: "" },
  { text: "25", status: "" },
  { text: "26", status: "" },
  { text: "27", status: "" },
  { text: "28", status: "" },
  { text: "29", status: "" },
  { text: "30", status: "" },
  { text: "31", status: "" },
  { text: "", status: "" },
  { text: "", status: "" },
  { text: "", status: "" },
])
const onBack = () => {
  uni.navigateBack()
}

const handleCheckin = async () => {
  if (hasCheckedIn.value) return

  try {
    // 这里调用签到API
    // const res = await checkinAPI();

    // 模拟签到成功
    hasCheckedIn.value = true

    uni.showToast({
      title: "签到成功！获得1工分",
      icon: "success",
      duration: 2000,
    })
  } catch (error) {
    uni.showToast({
      title: "签到失败，请重试",
      icon: "none",
    })
  }
}
</script>

<style lang="scss" scoped>
.score-task-page {
  min-height: 100vh;
  background: #f8f8f8;
  .top-card {
    background: #1686f9;
    border-bottom-left-radius: 32rpx;
    border-bottom-right-radius: 32rpx;
    padding: 32rpx 24rpx 0 24rpx;
    position: relative;
    display: flex;
    align-items: flex-start;
    .left-info {
      flex: 1;
      .days {
        color: #fff;
        font-size: 48rpx;
        font-weight: bold;
        .unit {
          font-size: 28rpx;
          margin-left: 4rpx;
        }
      }
      .desc {
        color: #fff;
        font-size: 26rpx;
        margin-top: 4rpx;
      }
      .reward {
        color: #fff;
        font-size: 26rpx;
        margin-top: 12rpx;
        .highlight {
          color: #ffe066;
          font-weight: bold;
        }
      }
    }
    .sign-btn {
      background: #ff7a00;
      color: #fff;
      border-radius: 32rpx;
      font-size: 30rpx;
      font-weight: bold;
      padding: 0 48rpx;
      height: 64rpx;
      margin-left: 24rpx;
      margin-top: 8rpx;
      border: none;
      box-shadow: 0 6rpx 16rpx rgba(255, 122, 0, 0.4);
      transition: all 0.3s ease;
      text-align: center;
      white-space: nowrap;

      &:active {
        transform: scale(0.95);
        box-shadow: 0 4rpx 12rpx rgba(255, 122, 0, 0.3);
      }

      &.disabled {
        background: rgba(255, 255, 255, 0.25);
        color: rgba(255, 255, 255, 0.9);
        box-shadow: 0 2rpx 8rpx rgba(255, 255, 255, 0.1);
        border: 2rpx solid rgba(255, 255, 255, 0.3);
        position: relative;
        padding: 0 48rpx 0 60rpx;

        &:active {
          transform: none;
        }

        &::before {
          content: "✓";
          position: absolute;
          left: 20rpx;
          top: 50%;
          transform: translateY(-50%);
          font-size: 28rpx;
          font-weight: bold;
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }
    .task-ill {
      width: 120rpx;
      height: 90rpx;
      position: absolute;
      right: 24rpx;
      bottom: 0;
    }
  }
  .month-bar {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #fff;
    border-radius: 0 0 24rpx 24rpx;
    margin: 0 24rpx;
    margin-top: -24rpx;
    box-shadow: 0 4rpx 12rpx 0 #e6e6e6;
    .month {
      font-size: 28rpx;
      color: #888;
      padding: 18rpx 32rpx;
      &.active {
        color: #1686f9;
        font-weight: bold;
        border-bottom: 4rpx solid #1686f9;
      }
    }
  }
  .calendar-card {
    background: #fff;
    border-radius: 24rpx;
    margin: 24rpx 24rpx 0 24rpx;
    padding: 24rpx 0 24rpx 0;
    box-shadow: 0 2rpx 8rpx 0 #f2f2f2;
    .calendar-head {
      display: flex;
      justify-content: space-between;
      padding: 0 24rpx;
      .calendar-week {
        color: #888;
        font-size: 26rpx;
        width: 48rpx;
        text-align: center;
      }
    }
    .calendar-body {
      display: flex;
      flex-wrap: wrap;
      padding: 0 24rpx;
      .calendar-day {
        width: 48rpx;
        height: 64rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 26rpx;
        color: #222;
        margin-bottom: 8rpx;
        &.signed {
          color: #1686f9;
          font-weight: bold;
        }
        &.miss {
          color: #bbb;
        }
        .signed-text {
          font-size: 20rpx;
          color: #1686f9;
          margin-top: 2rpx;
        }
        .miss-text {
          font-size: 20rpx;
          color: #bbb;
          margin-top: 2rpx;
        }
      }
    }
  }
  .rule-card {
    background: #fff;
    border-radius: 24rpx;
    margin: 24rpx 24rpx 0 24rpx;
    padding: 24rpx;
    .rule-title {
      font-size: 28rpx;
      color: #222;
      font-weight: 500;
      margin-bottom: 12rpx;
    }
    .rule-list {
      .rule-item {
        font-size: 26rpx;
        color: #666;
        margin-bottom: 8rpx;
        line-height: 1.7;
      }
    }
  }
}
</style>
