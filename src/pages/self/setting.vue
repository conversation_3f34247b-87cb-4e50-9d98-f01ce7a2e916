<template>
  <view class="setting-page">
    <!-- 顶部栏 -->
    <uni-nav-bar
      fixed
      status-bar
      title="系统设置"
      left-icon="left"
      left-text="返回"
      background-color="var(--primary-color)"
      color="#fff"
      @click-left="onBack"
    />
    <!-- 头像、昵称、性别等信息 -->
    <view class="setting-list">
      <view class="setting-item avatar-item">
        <text class="item-label">头像</text>
        <image
          class="avatar"
          :src="userInfo.avatar || '/static/default-avatar.png'"
          mode="aspectFill"
        />
      </view>
      <view class="setting-item">
        <text class="item-label">昵称</text>
        <text class="item-value">{{
          userInfo.nickname || "么麻子吃西瓜"
        }}</text>
      </view>
      <view class="setting-item" @click="onGender">
        <text class="item-label">性别</text>
        <text class="item-value">{{
          userInfo.gender === 2 ? "女" : "男"
        }}</text>
        <uni-icons type="right" size="18" color="#bbb" />
      </view>
      <view class="setting-item">
        <text class="item-label">手机号码</text>
        <text class="item-value">{{ userInfo.mobile || "13802158866" }}</text>
      </view>
      <view class="setting-item">
        <text class="item-label">绑定微信</text>
        <text class="item-value binded">已绑定</text>
        <text class="unbind-btn" @click.stop="onUnbindWechat">解绑</text>
      </view>
      <view class="setting-item">
        <text class="item-label">绑定QQ</text>
        <text class="item-value">未绑定</text>
      </view>
      <view class="setting-item" @click="onPassword">
        <text class="item-label">密码设置</text>
        <uni-icons type="right" size="18" color="#bbb" />
      </view>
      <view class="setting-item">
        <text class="item-label">清除缓存</text>
        <text class="item-value">32.49 KB</text>
      </view>
    </view>
    <!-- 隐私清单等 -->
    <view class="setting-list privacy-list">
      <view class="setting-item disabled">
        <text class="item-label">隐私清单</text>
      </view>
      <view class="setting-item" @click="onPersonalInfo">
        <text class="item-label">个人信息收集清单</text>
        <uni-icons type="right" size="18" color="#bbb" />
      </view>
      <view class="setting-item" @click="onShareInfo">
        <text class="item-label">第三方信息共享清单</text>
        <uni-icons type="right" size="18" color="#bbb" />
      </view>
    </view>
    <!-- 底部按钮区 -->
    <view class="setting-footer">
      <button class="cancel-btn" disabled>注销</button>
      <button class="logout-btn" @click="onLogout">退出登录</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
const userInfo = ref({
  avatar: "",
  nickname: "么麻子吃西瓜",
  gender: 2,
  mobile: "13802158866",
});
const onBack = () => {
  uni.navigateBack();
};
const onUnbindWechat = () => {
  uni.showToast({ title: "已解绑", icon: "none" });
};
const onGender = () => {
  // 性别选择弹窗逻辑
};
const onPassword = () => {
  // 跳转密码设置
};
const onPersonalInfo = () => {
  // 跳转个人信息收集清单
};
const onShareInfo = () => {
  // 跳转第三方信息共享清单
};
const onLogout = () => {
  uni.showModal({
    title: "提示",
    content: "确定要退出登录吗？",
    success: (res) => {
      if (res.confirm) {
        // 清除登录状态
        uni.reLaunch({ url: "/pages/login/login" });
      }
    },
  });
};
</script>

<style lang="scss" scoped>
.setting-page {
  min-height: 100vh;
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
  .setting-header {
    background: #1686f9;
    height: 100rpx;
    display: flex;
    align-items: center;
    padding: 0 32rpx;
    .setting-title {
      color: #fff;
      font-size: 36rpx;
      margin-left: 24rpx;
      font-weight: 500;
    }
  }
  .setting-list {
    background: #fff;
    border-radius: 16rpx;
    margin: 24rpx 24rpx 0 24rpx;
    overflow: hidden;
    .setting-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 32rpx;
      height: 96rpx;
      font-size: 30rpx;
      border-bottom: 1rpx solid #f0f0f0;
      .item-label {
        color: #222;
      }
      .item-value {
        color: #666;
      }
      .binded {
        color: #1686f9;
      }
      .unbind-btn {
        color: #ff7d00;
        margin-left: 24rpx;
        font-size: 28rpx;
      }
      &.avatar-item {
        .avatar {
          width: 72rpx;
          height: 72rpx;
          border-radius: 50%;
        }
      }
    }
    .setting-item:last-child {
      border-bottom: none;
    }
    &.privacy-list {
      margin-top: 24rpx;
      .setting-item.disabled {
        background: #f3f3f3;
        color: #bbb;
      }
    }
  }
  .setting-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 24rpx 24rpx 0 24rpx;
    .cancel-btn {
      flex: 1;
      background: #ededed;
      color: #bbb;
      border-radius: 8rpx;
      margin-right: 24rpx;
      font-size: 30rpx;
      height: 88rpx;
    }
    .logout-btn {
      flex: 1;
      background: #ff9000;
      color: #fff;
      border-radius: 8rpx;
      font-size: 30rpx;
      height: 88rpx;
    }
  }
}
</style>
