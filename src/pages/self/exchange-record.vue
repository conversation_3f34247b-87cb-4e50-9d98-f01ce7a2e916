<template>
  <view class="exchange-record-page">
    <!-- 顶部栏 -->
    <uni-nav-bar
      fixed
      status-bar
      title="兑换记录"
      left-icon="left"
      background-color="#1686f9"
      color="#fff"
      @click-left="onBack"
    >
      <template #right>
        <text class="service-btn">客服</text>
      </template>
    </uni-nav-bar>
    <!-- 温馨提示 -->
    <view class="tip-bar">
      <text class="tip-blue">温馨提醒：</text>
      所有兑换订单下单时间超过当日15：00的，将在最迟第二天一并发货，节假日顺延。
    </view>
    <!-- 确认按钮 -->
    <view class="confirm-btn">确定并收起</view>
    <!-- 兑换记录时间轴 -->
    <view class="timeline-list">
      <view v-for="(item, idx) in records" :key="idx" class="timeline-item">
        <view class="dot" :class="{ active: idx === 0 }"></view>
        <view class="item-content">
          <view class="item-date">{{ item.date }}</view>
          <view class="item-row">
            <text class="item-label">兑换积分：</text>
            <text class="item-score">{{ item.score }}</text>
            <text class="item-label">订单状态：</text>
            <text :class="['item-status', item.status === '结束' ? 'success' : '']">{{ item.status }}</text>
            <text class="item-detail">详情</text>
            <uni-icons type="right" size="16" color="#bbb" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
const records = ref([
  { date: '2025年02月27日', score: 2300, status: '发货中' },
  { date: '2025年02月27日', score: 2300, status: '结束' },
  { date: '2025年02月27日', score: 2300, status: '结束' },
  { date: '2025年02月27日', score: 2300, status: '结束' },
]);
const onBack = () => {
  uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.exchange-record-page {
  min-height: 100vh;
  background: #f8f8f8;
  .service-btn {
    color: #fff;
    font-size: 28rpx;
    margin-right: 24rpx;
  }
  .tip-bar {
    background: #fff;
    font-size: 26rpx;
    color: #666;
    padding: 24rpx 24rpx 0 24rpx;
    .tip-blue {
      color: #1686f9;
      font-size: 26rpx;
      font-weight: 500;
    }
  }
  .confirm-btn {
    color: #ff9000;
    font-size: 28rpx;
    text-align: center;
    margin: 16rpx 0 0 0;
    font-weight: 500;
  }
  .timeline-list {
    margin: 32rpx 0 0 0;
    padding: 0 0 0 36rpx;
    border-left: 2rpx solid #e5e5e5;
    .timeline-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 32rpx;
      position: relative;
      .dot {
        width: 16rpx;
        height: 16rpx;
        border-radius: 50%;
        background: #bbb;
        position: absolute;
        left: -36rpx;
        top: 8rpx;
        &.active {
          background: #1686f9;
          box-shadow: 0 0 0 6rpx #e3f0ff;
        }
      }
      .item-content {
        background: #fff;
        border-radius: 12rpx;
        padding: 18rpx 24rpx 12rpx 24rpx;
        box-shadow: 0 2rpx 8rpx 0 #f2f2f2;
        min-width: 540rpx;
        .item-date {
          font-size: 28rpx;
          color: #222;
          font-weight: 500;
        }
        .item-row {
          display: flex;
          align-items: center;
          margin-top: 12rpx;
          .item-label {
            color: #888;
            font-size: 26rpx;
            margin-right: 8rpx;
          }
          .item-score {
            color: #ff9000;
            font-size: 28rpx;
            font-weight: bold;
            margin-right: 24rpx;
          }
          .item-status {
            color: #888;
            font-size: 26rpx;
            margin-right: 24rpx;
            &.success {
              color: #1aad19;
            }
          }
          .item-detail {
            color: #888;
            font-size: 26rpx;
            margin-right: 4rpx;
          }
        }
      }
    }
  }
}
</style> 