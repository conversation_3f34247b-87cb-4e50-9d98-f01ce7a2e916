<template>
  <view class="checkin-page">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <view class="nav-content">
        <view class="nav-left" @click="handleBack">
          <uni-icons type="left" size="18" color="#fff" />
        </view>
        <view class="nav-title">工分任务</view>
        <view class="nav-right"></view>
      </view>
    </view>

    <!-- 顶部任务卡片 -->
    <view class="task-header-card">
      <view class="task-content">
        <view class="task-left">
          <view class="task-days">
            <text class="days-number">{{ consecutiveDays }}</text>
            <text class="days-text">天</text>
          </view>
          <view class="task-subtitle">您已经连续签到</view>
          <view class="task-desc"
            >连续签到<text class="highlight">{{ totalDays }}</text
            >天可获额外获得<text class="highlight">{{ totalReward }}</text
            >个工分</view
          >
        </view>
        <view class="task-right">
          <view class="gift-box">
            <image src="/static/images/gift-box.png" class="gift-icon" />
          </view>
          <button
            class="checkin-btn"
            :class="{ disabled: hasCheckedIn }"
            @click="handleCheckin"
            :disabled="hasCheckedIn"
          >
            {{ hasCheckedIn ? "已签到" : "签到" }}
          </button>
        </view>
      </view>

      <!-- 月份切换 - 在卡片底部 -->
      <view class="month-tabs">
        <view
          v-for="monthItem in displayMonths"
          :key="monthItem.index"
          class="month-tab"
          :class="{ active: monthItem.isActive }"
          @click="setMonth(monthItem.index)"
        >
          {{ monthItem.label }}
        </view>
      </view>
    </view>

    <!-- 日历组件 -->
    <view class="calendar-section">
      <swiper
        class="calendar-swiper"
        :current="currentMonthIndex"
        @change="onSwiperChange"
        :duration="300"
      >
        <swiper-item v-for="(monthData, index) in monthsData" :key="index">
          <view class="calendar-grid">
            <!-- 星期标题 -->
            <view class="week-header">
              <text v-for="week in weekDays" :key="week" class="week-day">{{
                week
              }}</text>
            </view>

            <!-- 日期网格 -->
            <view class="date-grid">
              <view
                v-for="(date, dateIndex) in monthData.dates"
                :key="dateIndex"
                class="date-item"
                :class="{
                  'other-month': date.isOtherMonth,
                  today: date.isToday,
                  checked: date.isChecked,
                  'past-unchecked': date.isPastUnchecked,
                  'current-month': !date.isOtherMonth,
                }"
              >
                <view class="date-content">
                  <text class="date-number">{{ date.day }}</text>
                </view>
                <view>
                  <text
                    v-if="date.isChecked && !date.isOtherMonth"
                    class="status-text"
                    >已签</text
                  >
                  <text
                    v-else-if="date.isPastUnchecked && !date.isOtherMonth"
                    class="status-text"
                    >未签</text
                  >
                </view>
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 签到规则说明 -->
    <view class="rules-section">
      <view class="rules-title">签到规则</view>
      <view class="rules-list">
        <text class="rule-item"
          >1.
          每日普通签到可获得1个工分，连续签到（两周）达到14天可获得10个工分。</text
        >
        <text class="rule-item">2. 签到任务每天可完成1次，从0点开始计算。</text>
        <text class="rule-item"
          >3. 连续签到中断后，需要重新开始计算连续天数。</text
        >
        <text class="rule-item"
          >4.
          在有网络（月网网络、月网网络）计算过程中，出现一次未签到，将会从额外工分奖励，一个月共计算次数外奖励过1分钟会。</text
        >
        <text class="rule-item"
          >5.
          工分币可以用作商品兑换，兑换规则详情工分商城产品兑换说明，本规则最终解释权归生活助手所有。</text
        >
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue"

// 响应式数据
const consecutiveDays = ref(3) // 连续签到天数
const totalDays = ref(14) // 总目标天数
const totalReward = ref(10) // 总奖励工分
const hasCheckedIn = ref(false) // 今日是否已签到
const currentDate = ref(new Date())
const currentMonthIndex = ref(6) // 当前选中的月份索引，默认选中当前月（数组中间位置）
const checkedDates = ref<string[]>([
  "2024-08-04",
  "2024-08-05",
  "2024-08-06",
  "2024-08-07",
  "2024-08-08",
  "2024-08-09",
  "2024-08-11",
  "2024-08-12",
]) // 已签到日期

// 星期标题
const weekDays = ["日", "一", "二", "三", "四", "五", "六"]

// 获取当前日期 - 修改为2024年8月以匹配设计稿
const today = new Date()
const currentYear = 2024 // 固定为2024年以匹配设计稿
const currentMonth = 7 // 8月（0-11，所以7代表8月）

// 生成单个月份的日历数据
const generateMonthData = (year: number, month: number) => {
  const today = new Date(2024, 7, 12) // 设置为2024年8月12日以匹配设计稿已签到状态

  // 获取当月第一天和最后一天
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)

  // 获取第一天是星期几
  const firstDayWeek = firstDay.getDay()

  const dates = []

  // 添加上个月的日期（填充）
  const prevMonth = new Date(year, month - 1, 0)
  for (let i = firstDayWeek - 1; i >= 0; i--) {
    const day = prevMonth.getDate() - i
    dates.push({
      day,
      isOtherMonth: true,
      isToday: false,
      isChecked: false,
      isPastUnchecked: false,
    })
  }

  // 添加当月日期
  for (let day = 1; day <= lastDay.getDate(); day++) {
    const dateStr = `${year}-${String(month + 1).padStart(2, "0")}-${String(
      day
    ).padStart(2, "0")}`
    const isToday =
      year === today.getFullYear() &&
      month === today.getMonth() &&
      day === today.getDate()

    const currentDateObj = new Date(year, month, day)
    const isChecked = checkedDates.value.includes(dateStr)
    const isPastUnchecked = currentDateObj < today && !isChecked && !isToday

    dates.push({
      day,
      isOtherMonth: false,
      isToday,
      isChecked,
      isPastUnchecked,
    })
  }

  // 添加下个月的日期（填充到42个格子）
  const remainingDays = 42 - dates.length
  for (let day = 1; day <= remainingDays; day++) {
    dates.push({
      day,
      isOtherMonth: true,
      isToday: false,
      isChecked: false,
      isPastUnchecked: false,
    })
  }

  return dates
}

// 生成所有月份的日历数据（前后各6个月，共13个月）
const monthsData = computed(() => {
  const months = []
  const baseYear = currentYear
  const baseMonth = currentMonth

  // 生成前后各6个月的数据
  for (let i = -6; i <= 6; i++) {
    const targetDate = new Date(baseYear, baseMonth + i, 1)
    months.push({
      year: targetDate.getFullYear(),
      month: targetDate.getMonth(),
      dates: generateMonthData(targetDate.getFullYear(), targetDate.getMonth()),
    })
  }

  return months
})

// 动态生成头部显示的月份标签
const displayMonths = computed(() => {
  const currentIndex = currentMonthIndex.value
  const months = monthsData.value

  if (months.length === 0) return []

  const result = []
  for (let i = -1; i <= 1; i++) {
    const index = currentIndex + i
    if (index >= 0 && index < months.length) {
      const monthData = months[index]
      const year = monthData.year
      const month = monthData.month + 1 // 显示时月份+1
      result.push({
        index: index,
        label: `${year}/${String(month).padStart(2, "0")}`,
        isActive: i === 0,
      })
    }
  }

  return result
})

// 方法
const handleBack = () => {
  uni.navigateBack()
}

const handleCheckin = async () => {
  if (hasCheckedIn.value) return

  try {
    // 这里调用签到API
    // const res = await checkinAPI();

    // 模拟签到成功
    hasCheckedIn.value = true
    consecutiveDays.value += 1

    // 添加今日签到记录
    const today = new Date(2024, 7, 12) // 2024年8月12日
    const todayStr = `${today.getFullYear()}-${String(
      today.getMonth() + 1
    ).padStart(2, "0")}-${String(today.getDate()).padStart(2, "0")}`
    checkedDates.value.push(todayStr)

    uni.showToast({
      title: "签到成功！获得1工分",
      icon: "success",
      duration: 2000,
    })
  } catch (error) {
    uni.showToast({
      title: "签到失败，请重试",
      icon: "none",
    })
  }
}

const setMonth = (index: number) => {
  if (index >= 0 && index < monthsData.value.length) {
    currentMonthIndex.value = index
    const selectedMonth = monthsData.value[index]
    currentDate.value = new Date(selectedMonth.year, selectedMonth.month, 1)
  }
}

// swiper切换事件
const onSwiperChange = (e: any) => {
  const current = e.detail.current
  setMonth(current)
}

// 页面初始化
onMounted(() => {
  // 设置默认显示当前月份（数组中间位置）
  setMonth(6)

  // 检查今日是否已签到
  const today = new Date(2024, 7, 12) // 2024年8月12日
  const todayStr = `${today.getFullYear()}-${String(
    today.getMonth() + 1
  ).padStart(2, "0")}-${String(today.getDate()).padStart(2, "0")}`
  hasCheckedIn.value = checkedDates.value.includes(todayStr)
})
</script>

<style lang="scss" scoped>
.checkin-page {
  min-height: 100vh;
  background: #f8f9fa;
}

.nav-bar {
  background: linear-gradient(135deg, #086bdb 0%, #4a9eff 100%);
  padding-top: var(--status-bar-height);

  .nav-content {
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24rpx;

    .nav-left {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }

    .nav-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #fff;
    }

    .nav-right {
      width: 60rpx;
    }
  }
}

.task-header-card {
  background: linear-gradient(135deg, #086bdb 0%, #4a9eff 100%);
  margin: 0 24rpx 24rpx 24rpx;
  border-radius: 20rpx;
  padding: 40rpx 32rpx 32rpx 32rpx;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 200rpx;
    height: 200rpx;
    background: url("/static/images/treasure-bg.png") no-repeat center;
    background-size: contain;
    opacity: 0.3;
  }

  .task-content {
    position: relative;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    // flex-direction: column;
    min-height: 160rpx;
  }

  .task-left {
    // display: flex;
    // align-items: flex-start;
    margin-bottom: 40rpx;
    padding-right: 160rpx;

    .task-days {
      display: flex;
      align-items: baseline;
      margin-right: 24rpx;
      flex-shrink: 0;

      .days-number {
        font-size: 96rpx;
        font-weight: bold;
        color: #fff;
        line-height: 0.9;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
      }

      .days-text {
        font-size: 32rpx;
        color: #fff;
        margin-left: 8rpx;
        font-weight: 500;
        margin-top: 8rpx;
      }
    }

    .task-info {
      flex: 1;
      padding-top: 16rpx;

      .task-subtitle {
        font-size: 32rpx;
        color: #fff;
        margin-bottom: 12rpx;
        line-height: 1.2;
        opacity: 0.95;
      }

      .task-desc {
        font-size: 26rpx;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.5;

        .highlight {
          color: #ffe066;
          font-weight: bold;
        }
      }
    }
  }

  .task-right {
    position: absolute;
    top: 40rpx;
    right: 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 3;

    .gift-box {
      width: 80rpx;
      height: 80rpx;
      margin-bottom: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .gift-icon {
        width: 64rpx;
        height: 64rpx;
      }
    }

    .checkin-btn {
      background: #ff7a00;
      color: #fff;
      font-size: 28rpx;
      font-weight: bold;
      border-radius: 28rpx;
      padding: 0 36rpx;
      height: 64rpx;
      line-height: 64rpx;
      border: none;
      box-shadow: 0 6rpx 16rpx rgba(255, 122, 0, 0.4);
      min-width: 100rpx;
      transition: all 0.3s ease;
      text-align: center;
      white-space: nowrap;

      &:active {
        transform: scale(0.95);
        box-shadow: 0 4rpx 12rpx rgba(255, 122, 0, 0.3);
      }

      &.disabled {
        background: rgba(255, 255, 255, 0.25);
        color: rgba(255, 255, 255, 0.9);
        box-shadow: 0 2rpx 8rpx rgba(255, 255, 255, 0.1);
        border: 2rpx solid rgba(255, 255, 255, 0.3);
        position: relative;
        padding: 0 40rpx 0 48rpx;

        &:active {
          transform: none;
        }

        &::before {
          content: "✓";
          position: absolute;
          left: 16rpx;
          top: 50%;
          transform: translateY(-50%);
          font-size: 24rpx;
          font-weight: bold;
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }
  }

  .month-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 20rpx;
    padding: 6rpx;
    gap: 4rpx;

    .month-tab {
      flex: 1;
      text-align: center;
      padding: 16rpx 0;
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.7);
      border-radius: 16rpx;
      transition: all 0.3s ease;
      font-weight: 500;

      &.active {
        background: rgba(255, 255, 255, 0.15);
        color: #fff;
        font-weight: bold;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10rpx);
      }
    }
  }
}

.calendar-section {
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);

  .calendar-swiper {
    height: 620rpx;
  }

  .calendar-grid {
    padding: 32rpx 24rpx 24rpx 24rpx;

    .week-header {
      display: flex;
      margin-bottom: 32rpx;

      .week-day {
        flex: 1;
        text-align: center;
        font-size: 28rpx;
        color: #4a9eff;
        font-weight: 500;
      }
    }

    .date-grid {
      display: flex;
      flex-wrap: wrap;

      .date-item {
        width: calc(100% / 7);
        height: 100rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        margin-bottom: 8rpx;

        .date-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 60rpx;
          height: 72rpx;
          position: relative;
        }

        .date-number {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
          line-height: 1;
          margin-bottom: 2rpx;
        }

        .status-text {
          font-size: 18rpx;
          line-height: 1;
          margin-top: 4rpx;
          position: absolute;
          bottom: -20rpx;
          left: 50%;
          transform: translateX(-50%);
        }

        &.other-month {
          .date-number {
            color: #ddd;
          }
        }

        &.today {
          .date-number {
            color: #086bdb;
            font-weight: bold;
          }
        }

        &.checked {
          .date-content {
            background: #086bdb;
            border-radius: 50%;
            width: 60rpx;
            height: 60rpx;
            box-shadow: 0 2rpx 8rpx rgba(8, 107, 219, 0.3);
          }

          .date-number {
            color: #fff;
            font-weight: bold;
            font-size: 26rpx;
            margin-bottom: 0;
          }

          .status-text {
            color: #086bdb;
            font-size: 16rpx;
            font-weight: 500;
          }
        }

        &.past-unchecked {
          .date-content {
            border: 2rpx solid #e5e5e5;
            border-radius: 50%;
            width: 60rpx;
            height: 60rpx;
            background: #fff;
          }

          .date-number {
            color: #999;
            font-size: 26rpx;
            margin-bottom: 0;
          }

          .status-text {
            color: #999;
            font-size: 16rpx;
          }
        }
      }
    }
  }
}

.rules-section {
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);

  .rules-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 24rpx;
  }

  .rules-list {
    .rule-item {
      display: block;
      font-size: 26rpx;
      color: #666;
      line-height: 1.8;
      margin-bottom: 16rpx;
      text-align: justify;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
