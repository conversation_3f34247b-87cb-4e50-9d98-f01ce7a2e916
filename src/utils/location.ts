class LocationService {
  amapKey = "";
  amapPlugin = null;
  constructor() {
    this.amapKey = import.meta.env.VITE_AMAP_KEY;
    this.amapPlugin = null;
  }

  // 获取省市区信息
  async getRegionInfo(longitude: number, latitude: number) {
    try {
      // #ifdef APP-PLUS
      console.log(plus);
      // #endif
      return await this._getByH5(longitude, latitude);

      // 其他平台可以继续扩展
      //   throw new Error("当前平台不支持");
    } catch (error) {
      console.error("获取地区信息失败:", error);
      throw error;
    }
  }

  // H5实现
  async _getByH5(longitude: number, latitude: number) {
    const res: any = await uni.request({
      method: "GET",
      url: "http://api.tianditu.gov.cn/geocoder",
      data: {
        key: this.amapKey,
        type: "geocode",
        ver: 1,
        postStr: `{"lon":"${longitude}","lat":"${latitude}"}`,
      },
    });

    if (res.data.status !== "1") {
      throw new Error(res.data.info || "逆地理编码失败");
    }

    return this._parseAmapResult(res.data.regeocode.addressComponent);
  }

  // 解析高德返回结果
  _parseAmapResult(addressComponent: any) {
    // 直辖市特殊处理（北京、上海、天津、重庆）
    const isMunicipality =
      !addressComponent.city ||
      addressComponent.city === addressComponent.province;

    return {
      province: {
        name: addressComponent.province,
        adcode: addressComponent.adcode.substr(0, 2) + "0000",
      },
      city: {
        name: isMunicipality
          ? addressComponent.province
          : addressComponent.city,
        adcode: isMunicipality
          ? addressComponent.adcode.substr(0, 2) + "0000"
          : addressComponent.adcode.substr(0, 4) + "00",
      },
      district: {
        name: addressComponent.district,
        adcode: addressComponent.adcode,
      },
      township: {
        name: addressComponent.township,
        adcode: addressComponent.towncode,
      },
    };
  }
}

export default new LocationService();
