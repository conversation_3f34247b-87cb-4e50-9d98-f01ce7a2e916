export const importFile = (url: string) => {
  // 条件编译
  // #ifdef H5
  return new URL(url, import.meta.url).href;
  // #endif
  // #ifdef APP-PLUS
  return url;
  // #endif
};

interface UploadFileOptionFiles {
  /**
   * multipart 提交时，表单的项目名，默认为 file，如果 name 不填或填的值相同，可能导致服务端读取文件时只能读取到一个文件。
   */
  name?: string;
  /**
   * 要上传的文件对象
   */
  file?: File;
  /**
   * 要上传文件资源的路径
   */
  uri?: string;
}
let UPLOAD_URL = "";
// #ifdef H5
UPLOAD_URL = import.meta.env.VITE_BASE_URL_H5 + "/storeapi/upload";
// #endif
// #ifdef APP-PLUS
UPLOAD_URL = import.meta.env.VITE_BASE_URL_APP + "/storeapi/upload";
// #endif
// uni-app 选择图片并上传
export const uploadFile = (count: number = 1) => {
  return new Promise((resolve, reject) => {
    uni.chooseImage({
      count: count,
      sizeType: ["compressed"],
      sourceType: ["album", "camera"],
      success: (res) => {
        // 添加加载动画
        uni.showLoading({
          title: "上传中",
          mask: true,
        });

        const uploadList = (res.tempFilePaths as string[]).map(
          (item, index) => {
            return {
              url: UPLOAD_URL,
              filePath: item,
              name: `files[${index}]`,
            };
          }
        );

        uni.uploadFile({
          url: UPLOAD_URL,
          files: uploadList as UploadFileOptionFiles[],
          header: {
            Authorization: "Bearer " + uni.getStorageSync("token") || "",
          },
          success: (res) => {
            // 隐藏加载动画
            uni.hideLoading();
            resolve(res.data);
          },
          fail: (err) => {
            // 隐藏加载动画
            uni.hideLoading();
            resolve(err);
          },
          complete: () => {
            uni.hideLoading();
          },
        });
      },
      fail: (err) => {
        // 隐藏加载动画
        uni.hideLoading();
        resolve(err);
      },
    });
  });
};
