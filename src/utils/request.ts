// 封装请求
const UPLOAD_URL = "https://upload.example.com";

interface RequestOptions extends UniNamespace.RequestOptions {
  url: string;
  method?:
    | "OPTIONS"
    | "GET"
    | "HEAD"
    | "POST"
    | "PUT"
    | "DELETE"
    | "TRACE"
    | "CONNECT";
  data?: any;
  header?: any;
  timeout?: number;
  dataType?: string;
  responseType?: string;
  sslVerify?: boolean;
  withCredentials?: boolean;
}
// 添加拦截器
const httpInterceptor = {
  // 拦截前触发
  invoke(options: UniApp.RequestOptions) {
    // 请求超时
    // options.timeout = 180000;
    options.header = {
      ...options.header,
    };
    // 添加 token 请求头标识
    const token = uni.getStorageSync("token");
    if (token) {
      options.header.Authorization = "Bearer " + token;
    }
    options.header["Content-Type"] = "application/json";

  },
};

uni.addInterceptor("request", httpInterceptor);

export const uniRequest = ({ url, method = "GET", data }: RequestOptions) => {
  let headers = {
    // 公共headers
    Authorization: "Bearer " + uni.getStorageSync("token"),
    // "Content-Type": "application/json",
  };
  console.log(url);
  return new Promise((resolve, reject) => {
    // console.log(headers);
    uni.request({
      url: url,
      method,
      // header: headers,
      data,
      dataType: "json", // 指定期望的数据类型
      success: (res: UniApp.RequestSuccessCallbackResult) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject(res);
        }
      },
      fail: (err) => {
        reject(err);
      },
    });
  });
};

export const uploadFile = (filePath: string) => {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: UPLOAD_URL + "/upload",
      filePath,
      name: "file",
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(JSON.parse(res.data));
        } else {
          reject(res);
        }
      },
      fail: (err) => {
        reject(err);
      },
    });
  });
};
