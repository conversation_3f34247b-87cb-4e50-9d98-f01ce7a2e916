<template>
  <div class="contact-page">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="客户管理"
      left-arrow
      right-text="新增"
      @click-left="onBack"
      @click-right="onAdd"
    />

    <!-- 搜索框 -->
    <div class="search-box">
      <van-search
        v-model="searchText"
        placeholder="请输入人名、客户名称、手机号码查询"
        shape="round"
      />
    </div>

    <!-- 空状态 -->
    <div v-if="!hasContacts" class="empty-state">
      <van-empty description="暂无客户" />
    </div>

    <!-- 联系人列表 -->
    <div v-else class="contact-list">
      <van-cell v-for="contact in filteredContacts" 
        :key="contact.id"
        :title="contact.name"
        :label="contact.carInfo"
      >
        <template #value>
          <div class="contact-info">
            <div class="info-row">
              <span class="phone">{{contact.phone}}</span>
              <van-tag v-if="contact.tag" 
                :type="contact.tag === 'VIP' ? 'primary' : 'danger'"
                round
              >{{contact.tag}}</van-tag>
            </div>
            <div class="action-buttons">
              <van-button size="small" type="primary" @click="handleCall(contact)">开单</van-button>
              <van-button size="small" plain @click="handleSales(contact)">统销</van-button>
              <van-button size="small" plain @click="handleCard(contact)">开卡</van-button>
              <van-button size="small" plain @click="handleDetails(contact)">详情</van-button>
            </div>
          </div>
        </template>
      </van-cell>
    </div>

    <!-- 底部导航 -->
    <van-tabbar v-model="activeTab">
      <van-tabbar-item icon="home-o">工作</van-tabbar-item>
      <van-tabbar-item icon="clock-o" badge="1">通知</van-tabbar-item>
      <van-tabbar-item icon="cloud-o">采购</van-tabbar-item>
      <van-tabbar-item icon="user-o" dot>客户</van-tabbar-item>
      <van-tabbar-item icon="setting-o">我的</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { showToast } from 'vant'

// 搜索文本
const searchText = ref('')
const activeTab = ref(3) // 默认选中客户tab

// 模拟联系人数据
const contacts = ref([
  { 
    id: 1, 
    name: '张贺然', 
    phone: '158****5568',
    carInfo: '粤CF521D',
    tag: 'VIP'
  },
  { 
    id: 2, 
    name: '四川省旅游股份公司', 
    phone: '158****5568',
    carInfo: '粤CF521D'
  },
  { 
    id: 3, 
    name: '赵国', 
    phone: '158****5568',
    carInfo: '粤CDF521D',
    tag: 'P'
  },
  { 
    id: 4, 
    name: '上谷套装', 
    phone: '158****5568',
    carInfo: '粤CF521D',
    tag: 'VIP'
  },
  { 
    id: 5, 
    name: '成都乐华时尚公司', 
    phone: '158****5568',
    carInfo: '粤CF521D',
    tag: 'P'
  }
])

// 过滤后的联系人列表
const filteredContacts = computed(() => {
  if (!searchText.value) return contacts.value
  return contacts.value.filter(contact => 
    contact.name.includes(searchText.value) || 
    contact.phone.includes(searchText.value) ||
    contact.carInfo.includes(searchText.value)
  )
})

// 是否有联系人
const hasContacts = computed(() => filteredContacts.value.length > 0)

// 返回上一页
const onBack = () => {
  history.back()
}

// 新增客户
const onAdd = () => {
  showToast('新增客户')
}

// 处理开单
const handleCall = (contact) => {
  showToast(`开单: ${contact.name}`)
}

// 处理统销
const handleSales = (contact) => {
  showToast(`统销: ${contact.name}`)
}

// 处理开卡
const handleCard = (contact) => {
  showToast(`开卡: ${contact.name}`)
}

// 处理查看详情
const handleDetails = (contact) => {
  showToast(`查看详情: ${contact.name}`)
}
</script>

<style scoped>
.contact-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f7f8fa;
}

.search-box {
  padding: 8px;
  background: #fff;
}

.contact-list {
  flex: 1;
  overflow-y: auto;
}

.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.phone {
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

:deep(.van-button--small) {
  min-width: 48px;
  padding: 0 8px;
}

:deep(.van-cell) {
  padding: 16px;
}

:deep(.van-cell__title) {
  flex: 1;
}

:deep(.van-cell__value) {
  flex: none;
}

:deep(.van-tag--round) {
  padding: 0 8px;
}

:deep(.van-nav-bar__text) {
  color: #1989fa;
}

:deep(.van-nav-bar .van-icon) {
  color: #1989fa;
}
</style> 